import request from '@/utils/request'

// 通用方法
// 獲取数据
export function getData(baseUrl, params) {
  return request({
    url: baseUrl,
    method: 'get',
    params
  })
}

// 新增、修改、删除数据
export function postData(baseUrl, type, num, data) {
  return request({
    url: baseUrl + num,
    method: type,
    data
  })
}

// 产品分类
// 獲取產品分類
export function productCategories(params) {
  return request({
    url: '/content-manager/collection-types/api::product-category.product-category',
    method: 'get',
    params
  })
}

// 新增、修改、删除产品分类
export function editProductCategories(type, num, data) {
  return request({
    url: '/content-manager/collection-types/api::product-category.product-category/' + num,
    method: type,
    data
  })
}

// 付款方式
// 獲取付款方式
const baseUrlPayment = '/content-manager/collection-types/api::payment-method.payment-method/'
export function getPayment(params) {
  return request({
    url: baseUrlPayment,
    method: 'get',
    params
  })
}

// 新增、修改、删除付款方式
export function editPayment(type, num, data) {
  return request({
    url: baseUrlPayment + num,
    method: type,
    data
  })
}

// 貨源列表
// 獲取貨源列表
const baseUrlSupply = '/content-manager/collection-types/api::supply.supply/'
export function getSupply(params) {
  return request({
    url: baseUrlSupply,
    method: 'get',
    params
  })
}

// 新增、修改、删除貨源
export function editSupply(type, num, data) {
  return request({
    url: baseUrlSupply + num,
    method: type,
    data
  })
}

// 产品列表
// 獲取产品列表
const baseUrlProduct = '/content-manager/collection-types/api::product.product/'
export function getProduct(params) {
  return request({
    url: baseUrlProduct,
    method: 'get',
    params
  })
}

// 新增、修改、删除产品
export function editProduct(type, num, data) {
  return request({
    url: baseUrlProduct + num,
    method: type,
    data
  })
}

