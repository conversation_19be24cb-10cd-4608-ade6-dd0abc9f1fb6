<template>
  <div class="app-container">
    <h1>试卷管理</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="试卷标题" width="200">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.title" :content="scope.row.title" placement="top">
            <span class="description-text">{{ scope.row.title }}</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="试卷类型" width="120">
        <template slot-scope="scope">
          <el-tag :type="getPaperTypeTag(scope.row.type)">
            {{ getPaperTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="totalScore" label="总分" width="100">
        <template slot-scope="scope">
          {{ scope.row.totalScore || 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="durationMinutes" label="答题时长" width="100">
        <template slot-scope="scope">
          {{ scope.row.durationMinutes || 0 }}分钟
        </template>
      </el-table-column>
      <el-table-column prop="course.name" label="所属科目" width="150" />
      <el-table-column label="题目数量" width="100">
        <template slot-scope="scope">
          <el-tag size="small" type="info">{{ (scope.row.paperQuestions && scope.row.paperQuestions.length) || 0
            }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button
            type="primary"
            icon="el-icon-edit"
            size="small"
            @click="handleClick(scope.row)"
          >
            {{ $t('table.edit') }}
          </el-button>
          <el-button
            type="success"
            icon="el-icon-link"
            size="small"
            style="margin-left: 8px;"
            @click="showExamLink(scope.row)"
          >
            试卷链接
          </el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
              }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="70%">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="120px"
        style="width: 90%; margin: 0 auto;">
        <el-form-item label="试卷标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入试卷标题" style="width: 100%" />
        </el-form-item>

        <el-form-item label="试卷描述" prop="description">
          <el-input type="textarea" v-model="form.description" :rows="3" placeholder="请输入试卷描述" />
        </el-form-item>

        <el-form-item label="试卷类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择试卷类型" style="width: 100%">
            <el-option value="mock_exam" label="模拟考试" />
            <el-option value="homework" label="作业" />
            <el-option value="quiz" label="随堂测验" />
            <el-option value="daily_practice" label="每日一练" />
            <el-option value="random_generated" label="随机组卷" />
          </el-select>
        </el-form-item>

        <el-form-item label="所属课程" prop="course">
          <el-select v-model="form.course" placeholder="请选择课程" style="width: 100%" filterable clearable>
            <el-option v-for="item in courseOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="答题时长" prop="durationMinutes">
          <el-input-number v-model="form.durationMinutes" :min="0" :max="300" label="分钟" />
        </el-form-item>

        <el-form-item label="试卷题目" prop="paperQuestions">
          <div v-for="(question, index) in form.paperQuestions" :key="index" class="question-item">
            <div class="question-header">
              <span class="question-index">第{{ index + 1 }}题</span>
              <el-button type="text" @click="removeQuestion(index)">删除</el-button>
          </div>
            <div class="question-content">
              <div class="question-stem">{{ question.stem }}</div>
              <div v-if="question.options" class="question-options">
                <div v-for="(option, optIndex) in question.options" :key="optIndex" class="option-item">
                  {{ String.fromCharCode(65 + optIndex) }}. {{ option.content }}
                  <el-tag size="mini" type="success" v-if="option.isCorrect">正确答案</el-tag>
                </div>
              </div>
              <div class="question-meta">
                <el-rate v-model="question.difficulty" disabled show-score text-color="#ff9900" :max="5" />
                <el-input-number v-model="question.score" :min="0" :max="100" size="small" style="margin-left: 20px" />
              </div>
            </div>
          </div>
          <el-button type="primary" @click="showQuestionDialog">添加题目</el-button>
        </el-form-item>

        <el-form-item label="总分" prop="totalScore">
          <el-input-number v-model="form.totalScore" :min="0" :max="1000" disabled />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary"
          @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 选择题目对话框 -->
    <el-dialog title="选择题目" :visible.sync="questionDialogVisible" width="70%">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="课程">
          <el-select v-model="questionQuery.course" placeholder="请选择课程" clearable @change="handleCourseChange">
            <el-option v-for="item in courseOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题型">
          <el-select v-model="questionQuery.type" placeholder="请选择题型" clearable>
            <el-option value="single_choice" label="单选题" />
            <el-option value="multiple_choice" label="多选题" />
            <el-option value="true_false" label="判断题" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchQuestions">查询</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="questionList" border style="width: 100%" v-loading="questionLoading" ref="questionTable"
        @selection-change="handleSelectionChange" @row-click="handleRowClick">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="stem" label="题目" min-width="300">
          <template slot-scope="scope">
            <div class="question-preview" @click.stop>
              <div class="question-stem">{{ scope.row.stem }}</div>
              <div v-if="scope.row.options" class="question-options">
                <div v-for="(option, index) in scope.row.options" :key="index" class="option-item">
                  {{ String.fromCharCode(65 + index) }}. {{ option.content }}
                  <el-tag size="mini" type="success" v-if="option.isCorrect">正确答案</el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="题型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getQuestionTypeTag(scope.row.type)">
              {{ getQuestionTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度" width="100">
          <template slot-scope="scope">
            <el-rate v-model="scope.row.difficulty" disabled show-score text-color="#ff9900" :max="5" />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="questionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addSelectedQuestions">确定</el-button>
      </div>
    </el-dialog>


    <el-dialog
      title="试题链接"
      :visible.sync="examLinkDialogVisible"
      width="500px"
    >
      <div style="display: flex; align-items: center;">
        <el-input
          v-model="examLink"
          readonly
          style="flex: 1; margin-right: 10px;"
        />
        <el-button type="primary" @click="copyExamLink">复制</el-button>
        <el-button type="waring" @click="openLink">打开链接</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from "axios"
import { getData, postData, uploadData, getRelationData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'
import relationHandler from '@/mixins/relationHandler'

export default {
  name: 'ExamPaper',
  components: { Pagination },
  mixins: [relationHandler],
  data() {
    return {
      list: [],
      total: 0,
      baseURL: 'http://localhost:1338',
      apiName: "exam-paper",
      listLoading: false,
      btnLoading: false,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {
        title: '',
        description: '',
        type: 'mock_exam',
        course: null,
        paperQuestions: [],
        totalScore: 0,
        durationMinutes: 60
      },
      formRow: {},
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },
      courseOptions: [],
      rules: {
        title: [{ required: true, message: '请输入试卷标题', trigger: 'blur' }],
        type: [{ required: true, message: '请选择试卷类型', trigger: 'change' }],
        course: [{ required: true, message: '请选择所属课程', trigger: 'change' }],
        paperQuestions: [{ required: true, message: '请添加试卷题目', trigger: 'change' }]
      },
      questionDialogVisible: false,
      questionList: [],
      questionLoading: false,
      questionQuery: {
        course: null,
        type: null,
        page: 1,
        pageSize: 10
      },
      selectedQuestions: [],
      examLinkDialogVisible: false,
      examLink: '',
    }
  },
  created() {
    this.getList(this.listQuery)
    this.getCourses()
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    getCourses() {
      getData('course').then(response => {
        this.courseOptions = response.results
      })
    },

    getPaperTypeTag(type) {
      const tags = {
        mock_exam: 'primary',
        homework: 'success',
        quiz: 'info',
        daily_practice: 'warning',
        random_generated: 'danger'
      }
      return tags[type] || 'info'
    },

    getPaperTypeText(type) {
      const texts = {
        mock_exam: '模拟考试',
        homework: '作业',
        quiz: '随堂测验',
        daily_practice: '每日一练',
        random_generated: '随机组卷'
      }
      return texts[type] || type
    },

    handleClick(row) {
      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.formRow = {
        id: row.id,
        documentId: row.documentId,
        title: row.title,
        description: row.description,
        type: row.type,
        course: row.course?.id,
        paperQuestions: row.paperQuestions || [],
        totalScore: row.totalScore || 0,
        durationMinutes: row.durationMinutes || 60
      }
      this.form = Object.assign({}, this.formRow)
    },

    handleCreate() {
      this.dialogStatus = 'create'
      this.resetForm()
      if (this.courseOptions && this.courseOptions.length > 0) {
        this.form.course = this.courseOptions[0].id
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    resetForm() {
      this.form = {
        title: '',
        description: '',
        type: 'mock_exam',
        course: null,
        paperQuestions: [],
        totalScore: 0,
        durationMinutes: 60
      }
    },

    showQuestionDialog() {
      this.questionDialogVisible = true
      this.searchQuestions()
    },

    searchQuestions() {
      this.questionLoading = true
      let params = {
        ...this.questionQuery,
        page: 1,
        pageSize: 1000,
        _q: ''
      }
      if (this.questionQuery.course) {
        params['filters[$and][0][course][id][$eq]'] = this.questionQuery.course
      }
      if (this.questionQuery.type) {
        params['filters[$and][1][type][$eq]'] = this.questionQuery.type
      }

      getData('question', params).then(response => {
        this.questionList = response.results
        this.questionLoading = false
        this.$nextTick(() => {
          this.setSelectedQuestions()
            })
          }).catch(error => {
        console.error('获取题目列表失败:', error)
        this.questionLoading = false
      })
    },

    setSelectedQuestions() {
      if (this.$refs.questionTable && this.questionList.length > 0) {
        const addedDocumentIds = this.form.paperQuestions.map(q => q.dId)
        this.questionList.forEach(row => {
          if (addedDocumentIds.includes(row.documentId)) {
            this.$refs.questionTable.toggleRowSelection(row, true)
          }
        })
      }
    },

    handleCourseChange() {
      this.questionQuery.type = null
      this.searchQuestions()
    },

    handleSelectionChange(selection) {
      this.selectedQuestions = selection
    },

    addSelectedQuestions() {
      if (this.selectedQuestions && this.selectedQuestions.length > 0) {
        const addedDocumentIds = this.form.paperQuestions.map(q => q.dId)
        
        const newQuestions = this.selectedQuestions.filter(question => 
          !addedDocumentIds.includes(question.documentId)
        )

        newQuestions.forEach(question => {
          const options = Array.isArray(question.options) ? question.options : JSON.parse(question.options)
          
          this.form.paperQuestions.push({
            dId: question.documentId,
            stem: question.stem,
            options: options,
            answer: question.answer,
            explanation: question.explanation,
            difficulty: question.difficulty,
            score: 5, // 默认分值
            // exampaper: {
            //   connect: [{
            //     id: this.form.id
            //   }]
            // }
          })
        })
        this.calculateTotalScore()
        this.questionDialogVisible = false
      } else {
        this.$message.warning('请选择要添加的题目')
      }
    },

    getQuestionTypeTag(type) {
      const tags = {
        single_choice: 'primary',
        multiple_choice: 'success',
        true_false: 'info'
      }
      return tags[type] || 'info'
    },

    getQuestionTypeText(type) {
      const texts = {
        single_choice: '单选题',
        multiple_choice: '多选题',
        true_false: '判断题'
      }
      return texts[type] || type
    },

    calculateTotalScore() {
      this.form.totalScore = this.form.paperQuestions.reduce((sum, question) => {
        return sum + (question.score || 0)
      }, 0)
    },

    createData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const uniqueQuestions = this.form.paperQuestions.reduce((acc, current) => {
            const x = acc.find(item => item.dId === current.dId);
            if (!x) {
              return acc.concat([current]);
            } else {
              return acc;
            }
          }, []);

          const tempData = {
            ...this.form,
            paperQuestions: uniqueQuestions,
            course: {
              connect: [{
                id: this.form.course
              }]
            }
          }

          this.btnLoading = true
          postData(this.apiName, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            this.btnLoading = false
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
          }).catch(error => {
            console.error('创建失败:', error)
            this.btnLoading = false
          })
        }
      })
    },

    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const uniqueQuestions = this.form.paperQuestions.reduce((acc, current) => {
            const x = acc.find(item => item.dId === current.dId);
            if (!x) {
              return acc.concat([current]);
            } else {
              return acc;
            }
          }, []);

          const tempData = {
            ...this.form,
            paperQuestions: uniqueQuestions,
            course: {
              connect: [{
                id: this.form.course
              }]
            }
          }

          postData(this.apiName, 'put', tempData.documentId, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          }).catch(error => {
            console.error('更新失败:', error)
          })
        }
      })
    },

    deleteDialog(row) {
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        postData(this.apiName, "DELETE", row.documentId, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    removeQuestion(index) {
      this.form.paperQuestions.splice(index, 1)
      this.calculateTotalScore()
    },

    handleRowClick(row) {
      this.$refs.questionTable.toggleRowSelection(row)
    },


    copyExamLink() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.examLink).then(() => {
          this.$message.success('链接已复制')
        })
      } else {
        const input = document.createElement('input')
        input.value = this.examLink
        document.body.appendChild(input)
        input.select()
        document.execCommand('copy')
        document.body.removeChild(input)
        this.$message.success('链接已复制')
      }
    },

    showExamLink(row) {
      if (row && row.id) {
        this.examLink = `https://qna.tefistudio.com/exam.html?id=${row.documentId}`
        this.examLinkDialogVisible = true
      } else {
        this.$message.warning('请先保存试卷，获取试卷ID后再生成链接')
      }
    },

    openLink() {
      if (this.examLink) {
        // 确保链接前缀正确（去掉@符号）
        let url = this.examLink
        if (url.startsWith('@')) {
          url = url.slice(1)
        }
        window.open(url, '_blank')
      } else {
        this.$message.warning('暂无可用链接')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.question-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  .question-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    .question-index {
      font-weight: bold;
    }
  }

  .question-content {
    .question-stem {
      margin-bottom: 10px;
      font-weight: 500;
    }

    .question-options {
      margin-bottom: 10px;

      .option-item {
        margin-bottom: 5px;
        padding: 5px 10px;
        background-color: #f5f7fa;
        border-radius: 4px;
      }
    }

    .question-meta {
      display: flex;
      align-items: center;
    }
  }
}

.description-text {
  display: inline-block;
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.question-preview {
  .question-stem {
    margin-bottom: 10px;
    font-weight: 500;
  }

  .question-options {
    .option-item {
      margin-bottom: 5px;
      padding: 5px 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}

.el-table {
  :deep(.el-table__row) {
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
