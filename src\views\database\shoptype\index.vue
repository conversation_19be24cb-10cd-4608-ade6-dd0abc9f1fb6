<template>
  <div class="app-container">
    <h1> ShopType </h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名稱（英文）" width="" />
      <el-table-column prop="name_tc" label="名稱（繁體）" width="" />
      <el-table-column prop="name_zh" label="名稱（简体）" width="" />
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-form-item label="名稱（英文）" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="名稱（繁體）" prop="name_tc">
          <el-input v-model="form.name_tc" />
        </el-form-item>
        <el-form-item label="名稱（簡體）" prop="name_zh">
          <el-input v-model="form.name_zh" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData, uploadData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      baseURL: 'http://localhost:1338',
      apiName: "shoptype", // 变量修改，请求主题名称
      listLoading: false,
      btnLoading: false,
      fileUploadType: false,
      // 弹框
      dialogFormVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },

      // 变量修改，表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_tc: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_zh: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    /*
    點擊修改的事件
    */
    handleClick(row) {
      // this.listLoading = true

      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      // 变量修改时的参数
      this.formRow = {
        id: row.id,
        name: row.name,
        name_tc: row.name_tc,
        name_zh: row.name_zh,
      }

      this.form = Object.assign({}, this.formRow) // copy obj
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let form = this.form

          const tempData = Object.assign({}, form)
          console.log(tempData)
          postData(this.apiName, 'put', tempData.id, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    清空提交表
    */
    resetForm() {
      // 变量修改，清零配置
      this.form = {
        name: "",
        name_tc: "",
        name_zh: "",
      }
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {

          // 设定通告的类型
          const temp = this.form
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          // 按鈕開始加載
          this.btnLoading = true
          postData(this.apiName, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            // 按鈕停止加載並關閉彈框
            this.btnLoading = false
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })

        }
      })
    },

    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      // 修改变量，如果分类下有数据则不允许删除
      if (row.shops.count === 0) {
        this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
          cancelButtonText: this.$t('table.cancel'),
          confirmButtonText: this.$t('table.confirm'),
          type: 'warning'
        }).then(() => {
          console.log(row.id)
          postData(this.apiName, "DELETE", row.id, '').then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Delete Successfully',
              type: 'success',
              duration: 1000
            })
          })
          this.$message({
            type: 'success',
            message: this.$t('table.deleteSuccess')
          })
          this.scopeId = ''
        }).catch(() => {
          this.$message({
            type: 'info',
            message: this.$t('table.deleteCancel')
          })
        })
      } else {
        this.$message({
          type: 'info',
          message: "該分類下有數據不允許刪除"
        })
      }
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}
</style>
