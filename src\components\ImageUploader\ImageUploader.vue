<template>
  <div v-loading="uploadLoading">
    <el-form-item :label="uploadName" >
      <el-upload class="upload-demo" :action="uploadUrl" :limit="1" :before-upload="handleBeforeUpload" :accept="accept"
        :show-file-list="false" :on-success="handleSuccess" :on-error="handleError">
        <el-button size="small" type="primary">选择文件</el-button>
        <span class="fileName">{{ fileName }}</span>
      </el-upload>
      <el-button size="small" @click="uploadFile" v-if="!uploadStatus && fileName"
        style="margin-top:10px">上传文件</el-button>
      <el-tag type="success" v-if="uploadStatus">文件上传成功</el-tag>
    </el-form-item>
  </div>
</template>

<script>
import { uploadData } from '@/api/requestData'
export default {
  props: {
    // 添加一个新的prop，用于触发重置逻辑
    resetTrigger: {
      type: Number,
      default: 0,
    },
    uploadUrl: {
      type: String,
      required: true,
    },
    accept: {
      type: String,
      default: 'image/JPEG,image/PNG,image/GIF,image/SVG,image/TIFF',
    },
    uploadType: {
      type: String,
      required: true,
    },
    uploadName: {
      type: String,
      // 使用default函数来实现当uploadName未传入时，默认使用uploadType的值
      default: function () {
        return this.uploadType;
      },
    },
  },
  data() {
    return {
      file: null,
      fileName: '',
      uploadLoading: false,
      // 将用于控制上传状态显示的变量更名为uploadStatus
      uploadStatus: false,
    };
  },
  methods: {
    handleBeforeUpload(file) {
      const acceptedFormats = /(image\/(JPEG|PNG|GIF|SVG|TIFF))|(image\/(jpeg|png|gif|svg|tiff))/;
      const isType = acceptedFormats.test(file.type);
      if (!isType) {
        this.$message.error("只能上传指定格式的图片");
      }
      this.file = file;
      this.fileName = file.name;
    },

    uploadFile() {
      // 上傳加載中
      this.uploadLoading = true
      const formData = new FormData();
      formData.append('files', this.file);

      uploadData(formData).then(response => {
        // loading結束，設定已完成上傳，設定fileID
        this.uploadLoading = false
        this.fileUploadType = true
        this.fileId = response[0].id
        this.$notify({
          title: '上傳成功',
          message: `文件${this.fileName}`,
          type: 'success'
        })
        this.handleSuccess(response[0])
      })
        .catch(error => {
          this.uploadLoading = false
          console.log('上传图片失败')
          this.handleError()
        });
    },
    handleSuccess(response) {
      this.uploadStatus = true; // 更新上传状态为成功
      // 在上传成功事件中传递文件ID和上传类型
      this.$emit('upload-success', { id: response.id, type: this.uploadType });
    },
    handleError() {
    },
    resetUploader() {
      this.uploadStatus = false;
      this.fileName = '';
      this.file = null;
    },
  },

  watch: {
    // 监听resetTrigger，当其变化时重置组件状态
    resetTrigger(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.resetUploader();
      }
    },
  },
};
</script>
<style scoped>

.fileName {
  display: inline-block;
  margin-left: 12px;
}
</style>
