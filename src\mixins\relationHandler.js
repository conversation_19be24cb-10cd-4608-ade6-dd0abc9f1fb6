export default {
  data() {
    return {
      // 用于存储各个关联字段的原始值
      relationPrevValues: {},
    }
  },
  methods: {
    /**
     * 初始化关联字段的跟踪
     * @param {string} field 关联字段名称
     * @param {Array} value 当前值
     */
    initRelationField(field, value = []) {
      this.$set(this.relationPrevValues, field, [...(value || [])])
    },

    /**
     * 重置关联字段的跟踪
     * @param {string|Array} fields 要重置的字段名称，可以是单个字段名或字段名数组
     */
    resetRelationFields(fields) {
      const fieldList = Array.isArray(fields) ? fields : [fields]
      fieldList.forEach(field => {
        this.$set(this.relationPrevValues, field, [])
      })
    },

    /**
     * 构建关联关系的 connect/disconnect 结构
     * @param {string} field 关联字段名称
     * @param {Array} currentIds 当前选中的 ID 数组
     * @param {Array} options 可选项数组，包含 id 和 documentId
     * @returns {Object} 返回 connect/disconnect 结构
     */
    buildRelation(field, currentIds = [], options = []) {
      const prevIds = this.relationPrevValues[field] || []
      const newIds = currentIds || []

      // 需要 connect 的（新选中但之前没选的）
      const connect = newIds
        .filter(id => !prevIds.includes(id))
        .map(id => {
          const item = options.find(c => c.id === id)
          return item ? { id: item.id, documentId: item.documentId } : null
        })
        .filter(Boolean)

      // 需要 disconnect 的（之前选了但现在没选的）
      const disconnect = prevIds
        .filter(id => !newIds.includes(id))
        .map(id => {
          const item = options.find(c => c.id === id)
          return item ? { id: item.id, documentId: item.documentId } : null
        })
        .filter(Boolean)

      return { connect, disconnect }
    },

    /**
     * 处理创建时的关联关系
     * @param {string} field 关联字段名称
     * @param {Array} currentIds 当前选中的 ID 数组
     * @param {Array} options 可选项数组，包含 id 和 documentId
     * @returns {Object} 返回只包含 connect 的结构
     */
    buildCreateRelation(field, currentIds = [], options = []) {
      return {
        connect: (currentIds || [])
          .map(id => {
            const item = options.find(c => c.id === id)
            return item ? { id: item.id, documentId: item.documentId } : null
          })
          .filter(Boolean),
        disconnect: []
      }
    }
  }
} 