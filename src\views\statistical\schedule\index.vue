<template>
  <div class="app-container">
    <h1>進度表</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item label="">
        <el-date-picker v-model="queryDate" type="daterange" range-separator="至" start-placeholder="開始日期"
          end-placeholder="結束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
        <el-button icon="el-icon-plus" type="primary" @click="newClick()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe @sort-change="handlesortChange">
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-table v-loading="listLoading" :data="props.row.content" border fit highlight-current-row
            style="width: 1400px">

            <el-table-column align="center" label="序号" min-width="30px">
              <template slot-scope="scope">
                <span>{{ (scope.$index + 1) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="company.name" label="公司" width="" />
            <el-table-column prop="quotationId" label="單票號" width="" />
            <el-table-column prop="name" label="項目名稱" width="120" />
            <el-table-column prop="description" label="進度描述" width="" />
            <el-table-column prop="remark" :label="$t('table.remark')" width="" />
            <el-table-column prop="state" :label="$t('table.status')" min-width="30px">
              <template slot-scope="row">
                <span>{{ row.state ? "已完成" : "未完成" }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('table.operate')" width="200">
              <template slot-scope="scope">
                <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope)">編輯</el-button>
                <el-popconfirm confirm-button-text="取消" cancel-button-text="删除" icon="el-icon-info" icon-color="red"
                  title="这确定删除吗？" @onCancel="deleteDialog(scope)">
                  <el-button slot="reference" type="danger" size="small" icon="el-icon-delete"
                    style="margin-left:12px">删除
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column prop="company.name" :label="$t('table.companyName')" width="120" sortable="custom" />
      <el-table-column prop="quotation.uuid" label="報價單號" width="120" sortable="custom" />
      <el-table-column prop="quotation.name" label="項目名稱" width="120" sortable="custom" />
      <el-table-column prop="startDate" label="開工日期" width="100" />
      <el-table-column prop="endDate" label="交貨日期" width="100" />

      <el-table-column prop="times" label="進度" width="80">
        <template slot-scope="scope">
          <template>
            <span>{{ scope.row.times = scope.row.content.length ?
                scope.row.content.length :
                '暫無'
            }}</span>
          </template>
        </template>
      </el-table-column>

      <el-table-column prop="remark" :label="$t('table.remark')">
        <template slot-scope="scope">
          <template>
            <span>{{ scope.row.remark = scope.row.content.length ?
                scope.row.content[scope.row.content.length - 1].remark :
                ''
            }}</span>

          </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="200">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleCreate(scope.row)">新增進度</el-button>

        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] + '進度'" :visible.sync="dialogFormVisible" width="70%">
      <el-form ref="form" :model="content" label-position="right" label-width="90px"
        style="width: 600px; margin-left:50px;">

        <el-form-item :label="$t('table.companyName')" prop="company">
          <el-input v-model="content[0].company" :disabled="true" />
        </el-form-item>
        <el-form-item label="單票號" prop="quotationId">
          <el-input v-model="content[0].quotationId" :disabled="true" />
        </el-form-item>
        <el-form-item label="項目名稱" prop="name">
          <el-input v-model="content[0].name" :disabled="true" />
        </el-form-item>
        <el-form-item label="進度描述" prop="description">
          <el-input v-model="content[0].description" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
        <el-form-item :label="$t('table.remark')" prop="remark">
          <el-input v-model="content[0].remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
        <el-form-item :label="$t('table.status')" prop="description">
          <el-radio v-model="content[0].state" :label="true">已完成</el-radio>
          <el-radio v-model="content[0].state" :label="false">未完成</el-radio>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{$t('table.cancel')}}
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? '新增進度' : '修改進度' }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog title="添加新的跟進" :visible.sync="dialogTableVisible" width="50%">
      <el-form ref="form" :model="form" label-position="right" label-width="70px"
        style="width: 600px; margin-left:50px;">
        <div class="box" style="width:270px">
          <h3>基本信息</h3>
          <el-form-item :label="$t('table.companyName')" prop="company">
            <el-select v-model="form.company" clearable filterable remote reserve-keyword class="filter-item"
              placeholder="Please select" :remote-method="remoteMethod_companyOptions" :loading="loading"
              default-first-option @change="changeCompany">
              <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="單票號" prop="quotation">
            <el-select v-model="form.quotation" clearable filterable remote reserve-keyword class="filter-item"
              placeholder="Please select" :remote-method="remoteMethod_quotationOptions" :loading="loading"
              default-first-option @change="changeQuotation">
              <el-option v-for="item in quotationOptions" :key="item.value" :label="item.label" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="項目名稱" prop="quotationName">
            <el-input v-model="form.quotationName" :disabled="true" />
          </el-form-item>
        </div>

        <div class="box" style="width:270px">
          <h3>日期</h3>
          <el-form-item label="開工日期" prop="startDate">
            <el-date-picker v-model="form.startDate" type="date" placeholder="選擇日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="交貨日期" prop="endDate">
            <el-date-picker v-model="form.endDate" type="date" placeholder="選擇日期">
            </el-date-picker>
          </el-form-item>

        </div>
      </el-form>


      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="createSchedule()">
          添加
        </el-button>
      </div>

    </el-dialog>


  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'
import otherTable from '@/components/otherTable'

export default {
  name: 'Turnover',
  components: { Pagination, otherTable },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::schedule.schedule/',
      companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      quotationUrl: '/content-manager/collection-types/api::quotation.quotation?filters[$and][0][state][$eq]=true',
      meUrl: '/admin/users/me/',
      list: [],
      totle: 0,
      // 登錄的用戶、操作的用戶
      userName: '',
      listLoading: false,
      loading: false,
      // 弹框
      dialogFormVisible: false,
      // table弹框
      dialogTableVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      // 父子table傳值
      tableId: '',
      time: '',
      // content
      content: [{}],
      queryDate: [],
      // content
      contentIndex: 0,
      // 修改數據
      formData: [],
      // 公司
      companyOptions: [],
      // 報價單
      quotationOptions: [],
      form: {},
      formRow: {},
      // 弹框属于發票還是發貨單的列表
      dialogTableStatus: '',
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'quotation.id:DESC',
        _q: ''
      },
      // 報價單的請求條件
      quotationQarams: {},
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 描述
        description: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // remark
        remark: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 獲取公司
    this.remoteMethod_companyOptions()
    // 獲取報價單
  },
  methods: {
    // 時間轉換函數
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        };
      };
      return fmt;
    },
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.results
        this.list = res
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字,第三個是限定條件
    async getRelation(url, p, target) {
      let params = ''
      let rt
      if (p && !target) {
        params = {
          'filters[$and][0][name][$contains]': p
        }
      } else if (p && target) {
        params = {
          'filters[$and][0][name][$contains]': p,
          'filters[$and][1][company][id][$eq]': target,
        }
      } else if (!p && target) {
        params = {
          'filters[$and][0][company][id][$eq]': target,
        }
      }

      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        rt = res
        this.listLoading = false
      })
      return rt
    },
    // 點擊修改的事件
    handleClick(scope) {
      console.log(scope.row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      console.log(scope.row);
      this.content = [{
        "id": row.id,
        "name": row.name,
        'quotationId': row.quotationId,
        'description': row.description,
        'remark': row.remark,
        "quotation": {
          "id": row.quotationId,
        },
        "company": {
          "name": row.company,
        },
        'state': false,
        "createBy": this.userName
      }]
      this.contentIndex = scope.$index

    },
    // 確認修改的事件
    updateData() {
      this.varyContent('update', this.content[0], this.contentIndex)
      this.dialogFormVisible = false
    },
    // 创建的按钮打开
    handleCreate(row) {
      this.listLoading = true
      this.content = [{
        id: row.id,
        quotationId: row.quotation.uuid,
        company: row.company.name,
        name: row.quotation.name,
        description: "",
        remark: "",
        state: false
      }]
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      console.log(row)
      console.log(this.content)
      this.listLoading = false
    },
    // 新增新的跟進按鈕
    newClick() {
      this.listLoading = true
      this.dialogTableVisible = true
      this.listLoading = false
    },
    // 提交新的跟進
    createSchedule() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const temp = {
            // 如果是關聯表，則用方法去判斷顯示什麼
            company: relationCreateReturn(this.form.company.value),
            quotation: relationCreateReturn(this.form.quotation.value),
            startDate: this.timeToTimestamp(this.form.startDate),
            endDate: this.timeToTimestamp(this.form.endDate),
            state: false,
            content: []
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogTableVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })

    },
    // 提交創建的事件
    createData() {
      console.log(this.content);
      this.varyContent('create', this.content[0], 0)
      this.dialogFormVisible = false
    },
    // 删除的最后确认按钮
    deleteDialog(scope) {
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        this.varyContent('delete', scope.row, scope.$index)
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },
    async varyContent(type, row, index) {
      const postForm = {}
      let params = {
        'filters[$and][0][id][$eq]': row.id
      }
      // 獲取用戶信息
      await getData(this.meUrl, '').then(response => {
        const res = response.data
        const name = res.firstname
        this.userName = name

      })
      await getData(this.baseUrl, params).then(response => {
        const res = response.results
        this.formData = res[0]

        // 如果有index，則改變content
        if (type === 'delete') {
          this.formData.content.splice(index, 1)
        } else if (type === 'update') {
          this.formData.content[index] = {
            "id": row.id,
            "name": row.name,
            'quotationId': row.quotationId,
            'description': row.description,
            'remark': row.remark,
            "quotation": {
              "id": row.quotationId,
            },
            "company": {
              "name": row.company,
            },
            'state': false,
            "createBy": this.userName
          }
        } else if (type === 'create') {
          console.log(row);
          let newRow = {
            "id": row.id,
            "name": row.name,
            'quotationId': row.quotationId,
            'description': row.description,
            'remark': row.remark,
            "quotation": {
              "id": row.quotationId,
            },
            "company": {
              "name": row.company,
            },
            'state': false,
            "createBy": this.userName
          }
          this.formData.content.push(newRow)
        }
        const tempData = Object.assign({}, this.formData)

        postData(this.baseUrl, 'put', row.id, tempData).then(() => {
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Update Successfully',
            type: 'success',
            duration: 1000
          })
        })
      })
    },
    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      // 如果選擇了時間，則把時間組合到搜索語句裡面去
      if (this.queryDate) {
        this.listQuery['filters[$and][0][createdAt][$gt]'] = this.timeToTimestamp(this.queryDate[0])
        this.listQuery['filters[$and][0][createdAt][$lt]'] = this.timeToTimestamp(this.queryDate[1])
      }
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 20,
        sort: 'quotation.id:DESC',
        _q: ''
      },
        this.queryDate = []
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },
    changeCountry(item) {
      this.form.currency = item.currency
    },
    // 新增或者修改时，新增电话或者手机的方法
    add(t) {
      if (t === 'telephone') {
        this.form.telephone.push('')
      } else if (t === 'phone') {
        this.form.phone.push('')
      }
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(t, delete_index) {
      if (t === 'telephone') {
        this.form.telephone.splice(delete_index, 1)
      } else if (t === 'phone') {
        this.form.phone.splice(delete_index, 1)
      }
    },

    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(delete_index) {
      this.form.content.splice(delete_index, 1)
    },

    // 排序的方法
    handlesortChange(column) {
      let target = ''
      console.log(column.prop)
      switch (column.prop) {
        case 'quotation.uuid':
          target = 'quotation.uuid'
          break;
        case 'company.name':
          target = 'company.name'
          break;
        case 'quotation.name':
          target = 'quotation.name'
          break;
        case 'uuid':
          target = 'uuid'
          break;

        default:
          break;
      }
      if (column.order == "ascending") {
        this.listQuery.sort = target + ":ASC"
      }
      if (column.order == "descending") {
        this.listQuery.sort = target + ":DESC"
      }
      this.getList(this.listQuery)
    },
    // 子傳父
    getDialogVisble(data) {
      this.dialogFormVisible = data
      this.getList(this.listQuery)
    },
    // 把時間轉化為時間戳
    timeToTimestamp(time) {
      let timestamp = Date.parse(new Date(time).toString());
      //timestamp = timestamp / 1000; //时间戳为13位需除1000，时间戳为13位的话不需除1000
      // console.log(time + "的时间戳为：" + timestamp);
      let d = new Date(timestamp);
      // console.log("dddd1111",d)
      d = d.toISOString()
      return d;
      //2021-11-18 22:14:24的时间戳为：1637244864707
    },

    // 遠程搜索 公司 remoteMethod_quotationOptions
    remoteMethod_companyOptions(query) {
      if (query !== '') {
        // 獲取公司
        this.getRelation(this.companyUrl, query).then((result) => {
          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              address: res[index].address
            }
            arrList.push(arr)
          }
          this.companyOptions = arrList
        })
      } else {
        this.getRelation(this.companyUrl).then((result) => {

          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              address: res[index].address
            }
            arrList.push(arr)
          }
          this.companyOptions = arrList
        })
      }

      console.log(this.companyOptions)
    },
    // 遠程搜索 報價單
    async remoteMethod_quotationOptions(query, target) {
      this.quotationQarams['filters[$and][0][schedules][id][$null]'] = true
      if (target) {
        this.quotationQarams['filters[$and][0][company][id][$eq]]'] = target
      }
      if (query) {
        this.quotationQarams["filters[$and][0][uuid][$contains]"] = query
      }else{
        delete this.quotationQarams["filters[$and][0][uuid][$contains]"]
      }
      await getData(this.quotationUrl, this.quotationQarams).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].uuid,
            name: res[index].name
          }
          arrList.push(arr)
        }
        this.quotationOptions = arrList
        this.listLoading = false
      })

      console.log(this.quotationOptions)
    },
    // 選擇公司後的操作
    changeCompany(item) {
      // 重新選擇公司後清空聯繫人
      this.$set(this.form, "quotation", '')
      this.$set(this.form, "quotationName", '')
      console.log(item);
      // 獲取公司下的報價單
      this.remoteMethod_quotationOptions("", item.value)
    },

    changeQuotation(item) {
      this.$set(this.form, "quotationName", item.name)
    },


  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}
</style>
