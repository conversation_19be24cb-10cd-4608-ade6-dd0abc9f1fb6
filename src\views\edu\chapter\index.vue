<template>
  <div class="app-container">
    <h1> Chapter </h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="章节标题" width="200" />
      <el-table-column prop="order" label="排序" width="80" />
      <el-table-column prop="course.name" label="所属课程" width="150" />
      <el-table-column prop="parentChapter.name" label="父章节" width="150">
        <template slot-scope="scope">
          {{ scope.row.parentChapter ? scope.row.parentChapter.name : '无' }}
        </template>
      </el-table-column>
      <el-table-column label="关联数据" width="300">
        <template slot-scope="scope">
          <el-tag size="small" type="info">子章节: {{(scope.row.subChapters && scope.row.subChapters.length) || 0}}</el-tag>
          <el-tag size="small" type="success">知识点: {{(scope.row.knowledgePoints && scope.row.knowledgePoints.length) || 0}}</el-tag>
          <el-tag size="small" type="warning">试题: {{(scope.row.questions && scope.row.questions.length) || 0}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 600px; margin-left:50px;">
        <el-form-item label="章节标题" prop="name">
          <el-input v-model="form.name" placeholder="如：第一章 趋势线" />
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="form.order" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="所属课程" prop="course">
          <el-select 
            v-model="form.course" 
            placeholder="请选择课程" 
            style="width: 100%"
            filterable
            clearable>
            <el-option
              v-for="item in courseOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父章节" prop="parentChapter">
          <el-select 
            v-model="form.parentChapter" 
            placeholder="请选择父章节（可选）" 
            style="width: 100%"
            :disabled="!form.course"
            filterable
            clearable>
            <el-option
              v-for="item in chapterOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="知识点" prop="knowledgePoints">
          <el-select 
            v-model="form.knowledgePoints" 
            multiple 
            placeholder="请选择知识点（可选）" 
            style="width: 100%"
            filterable
            clearable
            collapse-tags>
            <el-option
              v-for="item in knowledgePointOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData, uploadData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      baseURL: 'http://localhost:1338',
      apiName: "chapter",
      listLoading: false,
      btnLoading: false,
      fileUploadType: false,
      // 弹框
      dialogFormVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {
        name: '',
        order: 1,
        course: null,
        parentChapter: null,
        knowledgePoints: []
      },
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },
      courseOptions: [], // 课程选项
      chapterOptions: [], // 章节选项（用于选择父章节）
      knowledgePointOptions: [], // 知识点选项
      // 变量修改，表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入章节标题', trigger: 'blur' }
        ],
        order: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ],
        course: [
          { required: true, message: '请选择所属课程', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    this.getCourses()
    this.getKnowledgePoints()
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  watch: {
    // 监听课程选择变化，更新可选的父章节
    'form.course'(newVal) {
      if (newVal) {
        this.getChaptersByCourse(newVal)
      } else {
        this.chapterOptions = []
        this.form.parentChapter = null
      }
    }
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    /*
    获取课程列表
    */
    getCourses() {
      getData('course').then(response => {
        this.courseOptions = response.results
      })
    },

    /*
    获取指定课程的章节列表（用于选择父章节）
    */
    getChaptersByCourse(courseId) {
      getData('chapter', { course: courseId }).then(response => {
        // 过滤掉当前正在编辑的章节（如果是编辑模式）
        this.chapterOptions = response.results.filter(item => 
          item.id !== this.form.id
        )
      })
    },

    /*
    获取知识点列表
    */
    getKnowledgePoints() {
      getData('knowledgepoint').then(response => {
        this.knowledgePointOptions = response.results
      })
    },

    /*
    點擊修改的事件
    */
    handleClick(row) {
      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.formRow = {
        id: row.id,
        documentId: row.documentId,
        name: row.name,
        order: row.order,
        course: row.course?.id,
        parentChapter: row.parentChapter?.id,
        knowledgePoints: row.knowledgePoints?.map(item => item.id) || []
      }
      this.form = Object.assign({}, this.formRow)
      // 加载可选的父章节
      if (row.course) {
        this.getChaptersByCourse(row.course.id)
      }
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.form)
          console.log(tempData)
          postData(this.apiName, 'put', tempData.documentId, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    修改创建按钮的处理方法
    */
    handleCreate() {
      this.dialogStatus = 'create'  // 先设置状态
      this.resetForm()              // 再重置表单
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    重置表单方法保持不变
    */
    resetForm() {
      this.form = {
        name: '',
        order: 1,
        course: null,
        parentChapter: null,
        knowledgePoints: []
      }
      this.chapterOptions = []
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.form)
          this.btnLoading = true
          postData(this.apiName, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            this.btnLoading = false
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },

    // 删除的最后确认按钮
    deleteDialog(row) {
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        postData(this.apiName, "DELETE", row.documentId, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}

.description-text {
  display: inline-block;
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
