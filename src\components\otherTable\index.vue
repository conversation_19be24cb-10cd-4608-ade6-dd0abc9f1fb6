<template>
  <div class="app-container">
    <el-table :data="list" border stripe>
      <el-table-column prop="index" align="center" width="60" :label="$t('quotation.index')">
        <template slot-scope="scope">
          <template>
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </template>
      </el-table-column>

      <el-table-column prop="company" width="120" :label="$t('quotation.company')">
        <template slot-scope="{row}">
          <template>
            <span>{{ row.company ? row.company.name : '' }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="contact_person.name" :label="$t('quotation.contact')" />
      <el-table-column prop="name" :label="$t('quotation.project')" width="180" />

      <el-table-column prop="bill" v-if="dialogState == 'bill'" align="center" width="100"
        :label="$t('quotation.bill.name')">
        <template slot-scope="{row}">
          <span>{{ row.uuid }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="delivery" v-if="dialogState != 'bill'" align="center" width="100"
        :label="$t('quotation.delivery.name')">
        <template slot-scope="{row}">
          <span>{{ row.uuid }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="delivery" v-if="dialogState != 'bill'" align="center" width="80"
        :label="$t('quotation.delivery.recipient')">
        <template slot-scope="{row}">
          <span>{{ row.recipient }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="delivery" v-if="dialogState != 'bill'" align="center" width="120"
        :label="$t('quotation.delivery.recipientTel')">
        <template slot-scope="{row}">
          <span>{{ row.recipientTel }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="delivery" v-if="dialogState != 'bill'" align="center" width="200"
        :label="$t('quotation.delivery.recipientAddress')">
        <template slot-scope="{row}">
          <span>{{ row.recipientAddress }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="date" :label="$t('table.createDate')" width="100" />
      <el-table-column prop="currency" :label="$t('table.currency')" />
      <el-table-column prop="allPrice" :label="$t('table.total')" />

      <!-- <el-table-column prop="allPrice" label="總金額" /> -->

      <!-- 這一串是新增統計 -->
      <el-table-column prop="updatedAt" :label="$t('table.editDate')" width="120">
        <template slot-scope="{row}">
          <template>
            <span>{{ row.updatedAt.substring(0, 10) }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="updatedBy.firstname" :label="$t('table.editor')" />
      <el-table-column :label="$t('table.operate')" width="200">
        <template slot-scope="scope">
          <div v-if="dialogState != 'bill'">
            <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope)">{{ dialogState ===
                'bill' ? $t('table.invDetail') : $t('table.edit')
            }}</el-button>
            <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
              icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
              <el-button slot="reference" type="danger" size="small" icon="el-icon-delete"
                style="margin-left:12px">{{ $t('table.delete') }}
              </el-button>
            </el-popconfirm>
          </div>
          <div v-if="dialogState == 'bill'">
            <el-button type="primary" icon="el-icon-detail" size="small" @click="handleClick(scope)">{{$t('table.invDetail') }}</el-button>
            <!-- <el-popconfirm confirm-button-text="取消" cancel-button-text="删除" icon="el-icon-info" icon-color="red"
              title="这确定删除吗？" @onCancel="deleteDialog(scope.row)">
              <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">删除
              </el-button>
            </el-popconfirm> -->
          </div>
        </template>
      </el-table-column>

    </el-table>
    <el-dialog :title="(tableTextMap[dialogTableStatus] == '發票' ? $t('quotation.bill.name') : $t('quotation.delivery.name')) + ' '+ $t('table.detail')" :visible.sync="dialogFormVisible" append-to-body
      width="90%">
      <otherForm :formId="formId" :quotationId="quotationId" :typeName="tableTextMap[dialogTableStatus]"
        :childEvent="getDialogVisble" :key="timer" />
    </el-dialog>
  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import otherForm from '@/components/otherForm'
import { relationReturn, relationCreateReturn, getRelation, fillZero } from '@/api/tablefunction'

export default {
  name: 'table',
  components: { otherForm },
  props: ["tableId", "dialogState", "childFunction"],
  data() {
    return {
      // baseUrl: '/content-manager/collection-types/api::bill.bill/',
      baseUrl: this.dialogState == 'bill' ? '/content-manager/collection-types/api::bill.bill/' : '/content-manager/collection-types/api::delivery.delivery/',
      statisticalUrl: '/content-manager/collection-types/api::statistical.statistical/',
      // companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      // positionUrl: '/content-manager/collection-types/api::position.position?filters[$and][0][state][$eq]=true',
      list: [],
      listLoading: false,
      loading: false,
      // 弹框
      dialogFormVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      // 传给form的發票或者送貨單的id
      formId: '',
      quotationId: '',
      // 刷新子组件
      timer: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      // 弹框属于發票還是發貨單的列表
      dialogTableStatus: '',
      tableTextMap: {
        bill: '發票',
        delivery: '送貨單'
      },
      parms: {
        searchName: ''
      },
      // 公司
      companyOptions: [],
      // 职位
      positionOptions: [],
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        // page: 1,
        // pageSize: 20,
        // sort: 'id:ASC',
        // _q: ''
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 职位
        position: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 狀態
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ]
      }
    }
  },
  created() {

    this.listQuery = {
      'sort': 'uuid:ASC',
      'filters[$and][0][quotation][id][$eq]': this.tableId
    }
    this.getList(this.listQuery)
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        console.log('getlist');
        const res = response.results
        this.list = res
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字
    async getRelation(url, p) {
      let params = ''
      let rt
      if (p) {
        params = {
          'filters[$and][1][name][$contains]': p
        }
      }
      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name
          }
          arrList.push(arr)
        }
        this.listLoading = false
        rt = arrList
      })
      return rt
    },
    // 點擊修改的事件
    handleClick(scope) {
      // this.listLoading = true
      console.log(scope.row);
      this.dialogTableStatus = this.dialogState
      this.formId = scope.row.id
      this.quotationId = scope.row.quotation.id
      this.timer = new Date().getTime()
      this.dialogFormVisible = true
    },
    // 增加流水統計
    addTurnover(scope) {
      let row = scope.row
      console.log(row);
      // 創建成功後同步數據新增統計表
      const statisticaTemp = {
        // id
        quotation: relationCreateReturn(row.quotation.id),
        company: relationCreateReturn(row.company.id),
        bill: relationCreateReturn(row.id),
        finalCost: row.quotation.allCost,
        profit: row.quotation.allPrice - row.quotation.allCost,
        allReceive: 0,
        content: []
      }
      const statisticaData = Object.assign({}, statisticaTemp)
      postData(this.statisticalUrl, 'post', '', statisticaData).then(response => {
        // 創建成功後同步數據新增統計表
        this.dialogFormVisible = false
        this.$notify({
          title: 'Success',
          message: 'Create Successfully',
          type: 'success',
          duration: 1000
        })
      })
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.baseUrl, 'delete', row.id, '').then(() => {
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.childFunction(false)
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: '刪除成功!'
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消刪除'
        })
      })
    },

    // 新增或者修改时，新增电话或者手机的方法
    add(t) {
      if (t === 'telephone') {
        this.form.telephone.push('')
      } else if (t === 'phone') {
        this.form.phone.push('')
      }
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(t, delete_index) {
      if (t === 'telephone') {
        this.form.telephone.splice(delete_index, 1)
      } else if (t === 'phone') {
        this.form.phone.splice(delete_index, 1)
      }
    },
    // 遠程搜索
    remoteMethod(query) {
      if (query !== '') {
        // 獲取公司
        this.getRelation(this.companyUrl, query).then((result) => {
          this.companyOptions = result
        })
        console.log(this.companyOptions)
      } else {
        this.getRelation(this.companyUrl).then((result) => {
          this.companyOptions = result
        })
      }
    },
    // 子傳父
    getDialogVisble(data) {
      this.dialogFormVisible = data
      this.getList(this.listQuery)
    },

    // 提交按钮前的确认
    dialogConfirm(type, scope) {
      this.$confirm('是否继续?', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      }).then(() => {
        switch (type) {
          case 'addTurnover':
            this.addTurnover(scope)
            break;

          default:
            break;
        }

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}

.create {
  margin-left: 10px;
}
</style>
