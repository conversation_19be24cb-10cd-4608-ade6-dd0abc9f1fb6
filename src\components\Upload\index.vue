<template>
  <div class="upload-component">
    <div class="upload-button">
      <input
        type="file"
        ref="fileInput"
        accept=".apk"
        style="display: none"
        @change="onFileChange"
      >
      <el-button 
        size="small" 
        type="primary"
        @click="onClickUpload"
      >
        選擇文件
      </el-button>
      <span class="fileName">{{ fileName }}</span>
    </div>
    
    <div class="progress-wrapper" v-if="uploading || isProcessing">
      <el-progress 
        :percentage="uploadProgress"
        :status="progressStatus"
      />
      <div v-if="isProcessing" class="processing-status">
        <i class="el-icon-loading"></i>
        <span>正在處理文件...</span>
      </div>
    </div>
    
    <el-tag v-if="uploadSuccess" type="success">文件上傳成功</el-tag>
  </div>
</template>

<script>
import { uploadService } from '@/utils/request'
import { Message } from 'element-ui'

export default {
  name: 'FileUpload',
  
  props: {
    fileType: {
      type: String,
      default: 'application/vnd.android.package-archive'
    },
    typeErrorMessage: {
      type: String,
      default: '只能上傳 APK 文件！'
    },
    existingUrl: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      uploadProgress: 0,
      uploading: false,
      isProcessing: false,
      uploadSuccess: false,
      file: null,
      fileName: '',
      responseUrl: ''
    }
  },

  computed: {
    progressStatus() {
      return this.uploadSuccess ? 'success' : ''
    }
  },

  watch: {
    existingUrl: {
      immediate: true,
      handler(newUrl) {
        if (newUrl) {
          this.fileName = newUrl.split('/').pop()
          this.uploadSuccess = true
          this.responseUrl = newUrl
        }
      }
    }
  },

  methods: {
    onClickUpload() {
      this.$refs.fileInput.click()
    },

    onFileChange(event) {
      const file = event.target.files[0]
      if (!file) return

      if (file.type !== this.fileType) {
        Message.error(this.typeErrorMessage)
        event.target.value = ''
        return
      }

      this.uploadFile(file)
      event.target.value = ''
    },

    uploadFile(file) {
      this.uploading = true
      this.uploadProgress = 0
      this.isProcessing = false
      this.uploadSuccess = false
      this.file = file
      this.fileName = file.name

      const formData = new FormData()
      formData.append('file', file)

      return uploadService({
        url: '/upload',
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.lengthComputable) {
            const progress = Math.min(
              Math.round((progressEvent.loaded * 99) / progressEvent.total),
              99
            )
            this.uploadProgress = progress
            
            if (progress === 99) {
              this.uploading = false
              this.isProcessing = true
            }
          }
        }
      })
        .then(response => {
          this.isProcessing = false
          this.uploadProgress = 100
          this.uploadSuccess = true
          this.responseUrl = response.data.url
          
          this.$emit('upload-success', this.responseUrl)
          
          Message({
            type: 'success',
            message: `文件 ${this.fileName} 已上傳到服務器`
          })
          
          return response.data
        })
        .catch(error => {
          console.error('Upload error:', error)
          this.handleUploadError(error)
        })
    },

    handleUploadError(error) {
      console.error('Upload error:', error)
      this.uploading = false
      this.isProcessing = false
      this.uploadProgress = 0
      this.uploadSuccess = false
      this.responseUrl = ''
      
      this.$emit('upload-error', error)
      
      Message({
        type: 'error',
        message: error.message || `文件：${this.fileName} 上傳失敗`
      })
    },

    reset() {
      this.file = null
      this.fileName = ''
      this.uploadSuccess = false
      this.uploadProgress = 0
      this.uploading = false
      this.isProcessing = false
      this.responseUrl = ''
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-component {
  .upload-button {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .progress-wrapper {
    position: relative;
    margin: 10px 0;
    
    .el-progress {
      margin-bottom: 8px;
    }
  }

  .processing-status {
    display: flex;
    align-items: center;
    color: #409EFF;
    font-size: 14px;
    
    i {
      margin-right: 5px;
    }
  }

  .fileName {
    margin-left: 10px;
    color: #606266;
  }
}
</style>