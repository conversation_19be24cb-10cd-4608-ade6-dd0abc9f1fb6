<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1>{{ $t('route.product') }}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item label="">
        <el-input v-model="listQuery['filters[$and][0][supply][name][$contains]']"
          :placeholder="$t('table.querySupply')" />
      </el-form-item>
      <el-form-item label="">
        <el-input v-model="listQuery['filters[$and][0][product_category][name][$contains]']"
          :placeholder="$t('table.queryProductCategory')" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">{{ $t('table.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">{{ $t('table.reset') }}</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
        <el-button type="primary" plain icon="el-icon-download" @click="handleDownload()">Export Excel</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe @sort-change="handlesortChange">
      <el-table-column prop="id" label="ID" width="50" sortable="custom" />
      <el-table-column prop="product_category" :label="$t('table.category')" sortable="custom" width="90" />
      <el-table-column prop="name" :label="$t('table.productName')" width="120" sortable="custom" />
      <el-table-column prop="specification" :label="$t('table.spec')" />
      <el-table-column prop="describe" :label="$t('table.desc')" width="360">
        <template slot-scope="{row}">
          <!-- <el-tooltip placement="top">
            <div class="describe-tooltip" style="white-space: pre-wrap;" slot="content">{{ row.describe }}</div>
            <span class="describe">{{ row.describe }}</span>
          </el-tooltip> -->
          <el-popover placement="top" width="420" trigger="click">
            <div class="describe-tooltip" style="white-space: pre-wrap;">{{ row.describe }}</div>
            <el-button class="describe" slot="reference" type="text">{{ row.describe }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="supply" :label="$t('table.supply')" sortable="custom" width="90" />
      <el-table-column align="right" prop="cost" :label="$t('table.cost')" width="100">
        <template slot-scope="{row}">
          <span>{{ row.cost | getArea }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="price" :label="$t('table.total')" width="100">
        <template slot-scope="{row}">
          <span>{{ row.price | getArea }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" :label="$t('table.remark')" />
      <el-table-column prop="updatedAt" width="140" :label="$t('table.editDate')" />
      <el-table-column prop="updatedBy" :label="$t('table.editor')" width="74" />
      <el-table-column prop="stateType" :label="$t('table.status')" width="74" />
      <el-table-column :label="$t('table.operate')" width="200">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="120px"
        style="width: 400px; margin-left:50px;">

        <el-form-item :label="$t('table.productName')" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item :label="$t('table.category')">
          <el-select v-model="form.product_category" class="filter-item" placeholder="Please select">
            <el-option v-for="item in productCategoriesOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('table.spec')">
          <el-input v-model="form.specification" />
        </el-form-item>
        <el-form-item :label="$t('table.desc')">
          <el-input v-model="form.describe" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" />
        </el-form-item>

        <el-form-item :label="$t('table.supply')">
          <el-select v-model="form.supply" class="filter-item" placeholder="Please select">
            <el-option v-for="item in supplyOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('table.cost')">
          <el-input v-model="form.cost" />
        </el-form-item>
        <el-form-item :label="$t('table.total')" prop="price">
          <el-input v-model="form.price" />
        </el-form-item>
        <el-form-item :label="$t('table.remark')">
          <el-input v-model="form.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
        <el-form-item :label="$t('table.status')">
          <el-select v-model="form.stateType" class="filter-item" placeholder="Please select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getProduct, editProduct, getSupply, productCategories } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      totle: 0,
      listLoading: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      // 產品分類
      productCategoriesOptions: [],
      // 貨源
      supplyOptions: [],
      form: {
        name: '',
        remark: '',
        state: '',
        specification: '',
        describe: '',
        product_category: '',
        product_category_id: '',
        supply: '',
        supply_id: '',
        cost: '',
        price: ''
      },
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      },

      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司
        price: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    this.getProductCategories()
    this.getSupply()
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getProduct(params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            id: res[index].id,
            name: res[index].name,
            specification: res[index].specification ? res[index].specification : "",
            describe: res[index].describe ? res[index].describe : "",
            state: res[index].state,
            product_category: res[index].product_category ? res[index].product_category.name : "",
            product_category_id: res[index].product_category ? res[index].product_category.id : "",
            supply: res[index].supply ? res[index].supply.name : "",
            supply_id: res[index].supply ? res[index].supply.id : "",
            remark: res[index].remark ? res[index].remark : "",
            cost: res[index].cost ? res[index].cost : 0,
            price: res[index].price ? res[index].price : 0,
            stateType: res[index].state ? this.$t('table.status1') : this.$t('table.status0'),
            updatedAt: timeChange(res[index].updatedAt),
            updatedBy: res[index].updatedBy ? res[index].updatedBy.firstname : 'null'
          }
          arrList.push(arr)
        }
        this.list = arrList
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    // 排序的方法
    handlesortChange(column) {
      let target = ''
      switch (column.prop) {
        case 'product_category':
          target = 'product_category.name'
          break;
        case 'name':
          target = 'name'
          break;
        case 'id':
          target = 'id'
          break;
        case 'supply':
          target = 'supply.name'
          break;

        default:
          break;
      }
      if (column.order == "ascending") {
        this.listQuery.sort = target + ":ASC"
      }
      if (column.order == "descending") {
        this.listQuery.sort = target + ":DESC"
      }
      this.getList(this.listQuery)
    },
    // 獲取貨源
    getSupply(p) {
      let params = {
        'pageSize': 999
      }

      this.listLoading = true
      getSupply(params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name
          }
          arrList.push(arr)
        }
        this.supplyOptions = arrList
        this.listLoading = false
      })
    },
    // 獲取產品分類
    getProductCategories(p) {
      let params = {
        'pageSize': 999
      }

      this.listLoading = true
      productCategories(params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name
          }
          arrList.push(arr)
        }
        this.productCategoriesOptions = arrList
        this.listLoading = false
        console.log(this.productCategoriesOptions)
      })
    },
    // 點擊修改的事件
    handleClick(row) {
      // this.listLoading = true
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      const rowForm = {
        id: row.id,
        name: row.name,
        specification: row.specification,
        describe: row.describe,
        product_category: row.product_category,
        product_category_id: row.product_category_id,
        product_category: row.product_category,
        supply: row.supply,
        supply_id: row.supply_id,
        cost: row.cost,
        price: row.price,
        remark: row.remark,
        stateType: row.stateType,
        state: row.state
      }
      this.form = Object.assign({}, rowForm) // copy obj
    },
    // 確認修改的事件
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const temp = {
            id: this.form.id,
            name: this.form.name ? this.form.name : '',
            specification: this.form.specification ? this.form.specification : '',
            describe: this.form.describe ? this.form.describe : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            product_category: relationReturn(this.form.product_category.value, this.form.product_category_id),
            supply: relationReturn(this.form.supply.value, this.form.supply_id),
            cost: this.form.cost ? this.form.cost : 0,
            price: this.form.price ? this.form.price : 0,
            remark: this.form.remark ? this.form.remark : '',
            state: this.form.stateType.value != '0'
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          editProduct('put', temp.id, tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 清空提交表
    resetForm() {
      this.form = {
        name: '',
        remark: '',
        state: '',
        specification: '',
        describe: '',
        product_category: '',
        product_category_id: '',
        supply: '',
        supply_id: '',
        cost: '',
        price: '',
        stateType: ''
      }
    },
    // 创建的按钮打开
    handleCreate() {
      this.resetForm()
      this.form.stateType = '啟用'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    // 提交創建的事件
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          const temp = {
            name: this.form.name ? this.form.name : '',
            product_category: relationCreateReturn(this.form.product_category.value),
            supply: relationCreateReturn(this.form.supply.value),
            price: this.form.price ? this.form.price : '',
            state: this.form.stateType.value != '0'
          }
          if (this.form.specification) {
            temp.specification = this.form.specification
          }
          if (this.form.describe) {
            temp.describe = this.form.describe
          }
          if (this.form.cost) {
            temp.cost = this.form.cost
          }
          if (this.form.remark) {
            temp.remark = this.form.remark
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          editProduct('post', '', tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        editProduct('delete', row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },
    // 導出Excel
    handleDownload() {
      this.$confirm(this.$t('table.goonPrompt'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['ID', this.$t('table.category'), this.$t('table.productName'), this.$t('table.spec'), this.$t('table.desc'), this.$t('table.supply'), this.$t('table.cost'), this.$t('table.total'), this.$t('table.remark'), this.$t('table.editDate'), this.$t('table.editor')]
          const filterVal = ['id', 'product_category', 'name', 'specification', 'describe', 'supply', 'cost', 'price', 'remark', 'updatedAt', 'updatedBy']
          const data = this.formatJson(filterVal, this.list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.$t('route.product'),
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.cancelPrompt')
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },

    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      }
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },
    supplyQuery() {

    }

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.describe-tooltip {

  // width: 420px;
}
</style>
