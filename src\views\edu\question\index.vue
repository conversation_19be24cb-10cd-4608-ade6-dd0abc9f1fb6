<template>
  <div class="app-container">
    <h1>试题管理</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleBatchClick()">批量新增试题</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="stem" label="题目" width="300">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.stem" :content="scope.row.stem" placement="top">
            <span class="description-text">{{ scope.row.stem }}</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="题型" width="100">
        <template slot-scope="scope">
          <el-tag :type="getQuestionTypeTag(scope.row.type)">
            {{ getQuestionTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="difficulty" label="难度" width="100">
        <template slot-scope="scope">
          <el-rate v-model="scope.row.difficulty" disabled show-score text-color="#ff9900" :max="5">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column prop="course.name" label="所属科目" width="150" />
      <el-table-column label="关联数据" width="400">
        <template slot-scope="scope">
          <el-tag size="small" type="success">知识点: {{ (scope.row.knowledgepoints.count) || 0 }}</el-tag>
          <el-tag size="small" type="info">章节: {{ (scope.row.chapters.count) || 0 }}</el-tag>
          <el-tag size="small" type="warning">标签: {{ (scope.row.tags.count) || 0 }}</el-tag>
          <el-tag size="small" type="danger">附件: {{ (scope.row.attachments && scope.row.attachments.length) ||
            0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
            }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
              }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="70%">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="120px"
        style="width: 90%; margin: 0 auto;">
        <el-form-item label="所属课程" prop="course">
          <el-select v-model="form.course" placeholder="请选择课程" style="width: 100%" filterable clearable>
            <el-option v-for="item in courseOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="知识点" prop="knowledgepoints">
          <el-select v-model="form.knowledgepoints" multiple filterable clearable collapse-tags placeholder="请选择知识点"
            style="width: 100%" :disabled="!form.course" @change="handleKnowledgePointsChange">
            <el-option v-for="item in knowledgePointOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <div v-if="selectedKnowledgePointsDesc.length > 0" style="margin-top: 10px;">
            <div v-for="(desc, index) in selectedKnowledgePointsDesc" :key="index"
              style="margin-bottom: 8px; padding: 8px; background-color: #f5f7fa; border-radius: 4px;">
              <div style="font-weight: bold; margin-bottom: 4px;">{{ desc.name }}：</div>
              <div style="color: #606266; font-size: 13px;">{{ desc.description || '暂无描述' }}</div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="关联章节" prop="chapter">
          <el-select v-model="form.chapter" multiple filterable clearable collapse-tags placeholder="请选择章节"
            style="width: 100%" :disabled="!form.course">
            <el-option v-for="item in chapterOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="题型" prop="type">
          <el-select v-model="form.type" placeholder="请选择题型" style="width: 100%" @change="updateAnswerByOptions">
            <el-option value="single_choice" label="单选题" />
            <el-option value="multiple_choice" label="多选题" />
            <el-option value="true_false" label="判断题" />
            <!-- <el-option value="subjective" label="主观题" /> -->
          </el-select>
        </el-form-item>


        <el-form-item>
          <el-button type="primary" :disabled="!canGenerateAI" :loading="btn2Loading" @click="handleAIModelGenerate('gemini','f2.5')"
            icon="el-icon-magic-stick">
            AI生成(gemini-2.5flash)
          </el-button>
          <el-button type="primary" :disabled="!canGenerateAI" :loading="btn2Loading"@click="handleAIModelGenerate('silicon','qwen3')"
            icon="el-icon-magic-stick">
            AI生成(qwen3)
          </el-button>
          <el-button type="primary" :disabled="!canGenerateAI" :loading="btn2Loading" @click="handleAIModelGenerate('openai','gpt-4o-mini')"
            icon="el-icon-magic-stick">
            AI生成(gpt-4o)
          </el-button>

          <el-button type="info" :disabled="!canGenerateAI" @click="handleShowDetail" icon="el-icon-edit-outline">
            手动填写
          </el-button>
        </el-form-item>



        <el-form-item label="题目" prop="stem" v-show="showDetailForm">
          <el-input type="textarea" v-model="form.stem" :rows="4" />
        </el-form-item>

        <el-form-item label="选项" prop="options"
          v-if="['single_choice', 'multiple_choice'].includes(form.type) && showDetailForm">
          <div v-for="(option, index) in form.options" :key="index" style="margin-bottom: 10px;">
            <el-input v-model="option.content" style="width: calc(100% - 120px)">
              <template slot="prepend">选项 {{ String.fromCharCode(65 + index) }}</template>
            </el-input>
            <el-checkbox v-model="option.isCorrect" style="margin-left: 10px" @change="updateAnswerByOptions">
              正确答案
            </el-checkbox>
            <el-button type="text" @click.prevent="removeOption(index)" style="margin-left: 10px">删除</el-button>
          </div>
          <el-button type="text" @click="addOption">添加选项</el-button>
        </el-form-item>

        <el-form-item label="标准答案" prop="answer" v-if="showDetailForm">
          <el-input type="textarea" v-model="form.answer" :rows="3" />
        </el-form-item>

        <el-form-item label="答案解析" prop="explanation" v-if="showDetailForm">
          <el-input type="textarea" v-model="form.explanation" :rows="3" />
        </el-form-item>


        <el-form-item label="难度" prop="difficulty" v-if="showDetailForm">
          <el-rate v-model="form.difficulty" :max="5" show-score>
          </el-rate>
        </el-form-item>


        <el-form-item label="标签" prop="tag" v-if="showDetailForm">
          <el-select v-model="form.tag" multiple filterable allow-create default-first-option clearable collapse-tags
            placeholder="请选择或创建标签" style="width: 100%">
            <el-option v-for="item in tagOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="附件" prop="attachments" v-if="showDetailForm">
          <el-upload :action="uploadUrl" :file-list="form.attachments" :on-success="handleUploadSuccess"
            :on-remove="handleUploadRemove" multiple>
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" v-if="showDetailForm"
          @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :title="'批量新增试题'" :visible.sync="batchDialogVisible" width="70%">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="120px"
        style="width: 90%; margin: 0 auto;">
        <el-form-item label="所属课程" prop="course">
          <el-select v-model="form.course" placeholder="请选择课程" style="width: 100%" filterable clearable>
            <el-option v-for="item in courseOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="知识点" prop="knowledgepoints">
          <el-select v-model="form.knowledgepoints" multiple filterable clearable collapse-tags placeholder="请选择知识点"
            style="width: 100%" :disabled="!form.course" @change="handleKnowledgePointsChange">
            <el-option v-for="item in knowledgePointOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <div v-if="selectedKnowledgePointsDesc.length > 0" style="margin-top: 10px;">
            <div v-for="(desc, index) in selectedKnowledgePointsDesc" :key="index"
              style="margin-bottom: 8px; padding: 8px; background-color: #f5f7fa; border-radius: 4px;">
              <div style="font-weight: bold; margin-bottom: 4px;">{{ desc.name }}：</div>
              <div style="color: #606266; font-size: 13px;">{{ desc.description || '暂无描述' }}</div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="关联章节" prop="chapter">
          <el-select v-model="form.chapter" multiple filterable clearable collapse-tags placeholder="请选择章节"
            style="width: 100%" :disabled="!form.course">
            <el-option v-for="item in chapterOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="批量输入内容" prop="batchInput">
          <el-input type="textarea" v-model="batchInput" :rows="4" />
          <div class="input-tip">
            <span class="word-count">{{ batchInputLength }}/2000</span>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="handleBatchImport">
          转换
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量选择试题弹框 -->
    <el-dialog
      title="批量选择试题"
      :visible.sync="batchSelectDialogVisible"
      width="70%">
      <el-table
        :data="batchQuestions"
        border
        ref="batchTable"
        @selection-change="handleBatchSelectionChange"
        style="width: 100%">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="stem" label="题目" min-width="300">
          <template slot-scope="scope">
            <div class="question-preview" @click.stop>
              <div class="question-stem" style="font-weight: bold;">
                <el-tooltip :content="scope.row.stem" placement="top" effect="dark">
                  <span>{{ scope.row.stem }}</span>
                </el-tooltip>
              </div>
              <div v-if="scope.row.options && scope.row.options.length" class="question-options" style="margin-top: 4px;">
                <div v-for="(option, index) in scope.row.options" :key="index" class="option-item" style="margin-bottom: 2px;">
                  {{ String.fromCharCode(65 + index) }}. {{ option.content }}
                  <el-tag size="mini" type="success" v-if="option.isCorrect" style="margin-left: 6px;">正确答案</el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="difficulty" label="难度" width="100">
          <template slot-scope="scope">
            <el-rate v-model="scope.row.difficulty" disabled show-score text-color="#ff9900" :max="5" />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchSelectDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="handleBatchAddConfirm">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import axios from "axios"
import { getData, postData, uploadData, getRelationData, requestAIModel } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'
import relationHandler from '@/mixins/relationHandler'

export default {
  name: 'Product',
  components: { Pagination },
  mixins: [relationHandler],
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      baseURL: 'http://localhost:1338',
      apiName: "question",
      listLoading: false,
      btnLoading: false,
      fileUploadType: false,
      btn2Loading: false,
      // 弹框
      dialogFormVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {
        stem: '',
        type: 'single_choice',
        options: [], // 改为直接使用数组，不再使用 JSON 字符串
        answer: '',
        explanation: '',
        difficulty: 3,
        course: null,
        knowledgepoints: [],
        chapter: [],
        tags: [],
        attachments: []
      },
      formRow: {},
      questionData: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 1000,
        sort: 'id:ASC',
        _q: ''
      },
      courseOptions: [], // 课程选项
      chapterOptions: [], // 章节选项
      knowledgePointOptions: [], // 知识点选项（用于选择父知识点）
      tagOptions: [],
      uploadUrl: this.baseURL + '/upload',
      // 变量修改，表单验证规则
      rules: {
        stem: [{ required: true, message: '请输入题目', trigger: 'blur' }],
        type: [{ required: true, message: '请选择题型', trigger: 'change' }],
        answer: [{ required: true, message: '请输入标准答案', trigger: 'blur' }],
        course: [{ required: true, message: '请选择所属课程', trigger: 'change' }],
        knowledgepoints: [{ required: true, message: '请选择知识点', trigger: 'change' }]
      },
      selectedKnowledgePointsDesc: [], // 存储选中知识点的描述
      showDetailForm: false, // 控制表单详细内容的显示
      prompt: "",
      batchDialogVisible: false,
      batchInput: '', // 批量输入内容
      batchSelectDialogVisible: false,
      batchQuestions: [],
      batchSelectedQuestions: [],
    }
  },
  computed: {
    // 判断是否可以生成 AI 内容
    canGenerateAI() {
      return this.form.course &&
        this.form.knowledgepoints.length > 0 &&
        this.form.chapter.length > 0 &&
        this.form.type
    },
    batchInputLength() {
      return this.batchInput ? this.batchInput.length : 0
    }
  },
  created() {
    this.getList(this.listQuery)
    this.getCourses()
    this.getTags()
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl


  },
  watch: {
    // 监听课程选择变化
    'form.course'(newVal) {
      if (newVal) {
        console.log(newVal);
        this.form.knowledgepoints = []

        this.getChaptersByCourse(newVal)
        this.getKnowledgePointsByCourse(newVal)
      } else {
        this.chapterOptions = []
        this.knowledgePointOptions = []
        this.form.knowledgepoints = []
        this.form.chapter = []
      }
    }
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    /*
    获取课程列表
    */
    getCourses() {
      getData('course').then(response => {
        this.courseOptions = response.results
      })
    },

    /*
    获取所有标签列表
    */
    getTags() {
      getData('tag').then(response => {
        this.tagOptions = response.results
      })
    },

    /*
    获取指定课程的章节列表
    */
    getChaptersByCourse(courseId) {
      getData('chapter', { course: courseId }).then(response => {
        this.chapterOptions = response.results
      })
    },

    /*
    获取指定课程的知识点列表（用于选择父知识点）
    */
    getKnowledgePointsByCourse(courseId) {
      // 获取课程名称
      const course = this.courseOptions.find(item => item.id === courseId)
      if (!course) return

      // 构建筛选条件
      const params = {
        'filters[$and][0][course][id][$eq]': course.id,
        'pageSize': 1000,
        'page': 1
      }

      getData('knowledgepoint', params).then(response => {
        // 过滤掉当前正在编辑的知识点
        this.knowledgePointOptions = response.results.filter(item =>
          item.id !== this.form.id
        )
      })
    },

    /*
    获取试题关联的知识点列表
    */
    getKnowledgePointsByQuestion(questionId, documentId) {
      return getRelationData('question', 'knowledgepoints', documentId, {
        pageSize: 100
      }).then(response => {
        return response.results || []
      }).catch(error => {
        console.error('获取知识点关联失败:', error)
        return []
      })
    },

    /*
    获取试题关联的章节列表
    */
    getChaptersByQuestion(questionId, documentId) {
      return getRelationData('question', 'chapters', documentId, {
        pageSize: 100
      }).then(response => {
        return response.results || []
      }).catch(error => {
        console.error('获取章节关联失败:', error)
        return []
      })
    },

    /*
    获取试题关联的标签列表
    */
    getTagsByQuestion(questionId, documentId) {
      return getRelationData('question', 'tags', documentId, {
        pageSize: 100
      }).then(response => {
        return response.results || []
      }).catch(error => {
        console.error('获取标签关联失败:', error)
        return []
      })
    },

    /*
    點擊修改的事件
    */
    handleClick(row) {
      console.log('编辑行数据:', row)
      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.showDetailForm = true

      this.formRow = {
        id: row.id,
        documentId: row.documentId,
        stem: row.stem || '',
        type: row.type,
        options: Array.isArray(row.options) ? row.options : [],
        answer: row.answer || '',
        explanation: row.explanation || '',
        difficulty: row.difficulty || 3,
        course: row.course?.id,
        knowledgepoints: [],
        chapter: [],
        tag: [],
        attachments: row.attachments || []
      }

      this.form = Object.assign({}, this.formRow)

      if (this.form.course) {
        this.getChaptersByCourse(this.form.course)
        this.getKnowledgePointsByCourse(this.form.course)

        // 获取关联的知识点、章节和标签
        Promise.all([
          this.getKnowledgePointsByQuestion(row.id, row.documentId),
          this.getChaptersByQuestion(row.id, row.documentId),
          this.getTagsByQuestion(row.id, row.documentId)
        ]).then(([knowledgepoints, chapters, tags]) => {
          this.form.knowledgepoints = knowledgepoints.map(item => item.id)
          this.form.chapter = chapters.map(item => item.id)
          this.form.tag = tags.map(item => item.id)
          // 使用 mixin 方法初始化关联字段
          this.initRelationField('knowledgepoints', this.form.knowledgepoints)
          this.initRelationField('chapter', this.form.chapter)
          this.initRelationField('tag', this.form.tag)
        })
      }
    },

    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = {
            ...this.form,
            stem: this.form.stem.trim(),
            options: this.form.options
          }

          // 使用 mixin 方法构建关联关系
          tempData.knowledgepoints = this.buildRelation('knowledgepoints', this.form.knowledgepoints, this.knowledgePointOptions)
          tempData.chapters = this.buildRelation('chapter', this.form.chapter, this.chapterOptions)
          tempData.tags = this.buildRelation('tag', this.form.tag, this.tagOptions)

          console.log('更新数据:', tempData)

          postData(this.apiName, 'put', tempData.documentId, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          }).catch(error => {
            console.error('更新失败:', error)
          })
        }
      })
    },

    /*
    修改创建按钮的处理方法
    */
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetForm()
      this.dialogFormVisible = true
      this.showDetailForm = false // 重置表单显示状态
      if (this.courseOptions && this.courseOptions.length > 0) {
        this.form.course = this.courseOptions[0].id
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    重置表单方法
    */
    resetForm() {
      this.form = {
        stem: '',
        type: 'single_choice',
        options: [{ content: '', isCorrect: false }],
        answer: '',
        explanation: '',
        difficulty: 3,
        course: null,
        knowledgepoints: [],
        chapter: [],
        tag: [],
        attachments: []
      }
      // 使用 mixin 方法重置关联字段
      this.resetRelationFields(['knowledgepoints', 'chapters', 'tags'])
      this.chapterOptions = []
      this.knowledgePointOptions = []
      this.selectedKnowledgePointsDesc = []
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = {
            ...this.form,
            stem: this.form.stem.trim(),
            options: this.form.options
          }

          // 使用 mixin 方法构建创建时的关联关系
          tempData.knowledgepoints = this.buildCreateRelation('knowledgepoints', this.form.knowledgepoints, this.knowledgePointOptions)
          tempData.chapters = this.buildCreateRelation('chapter', this.form.chapter, this.chapterOptions)
          tempData.tags = this.buildCreateRelation('tag', this.form.tag, this.tagOptions)

          console.log('创建数据:', tempData)

          this.btnLoading = true
          postData(this.apiName, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            this.btnLoading = false
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
          }).catch(error => {
            console.error('创建失败:', error)
            this.btnLoading = false
          })
        }
      })
    },

    // 删除的最后确认按钮
    deleteDialog(row) {
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        postData(this.apiName, "DELETE", row.documentId, '').then(() => {
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    getQuestionTypeTag(type) {
      const tags = {
        single_choice: 'primary',
        multiple_choice: 'success',
        true_false: 'info',
        subjective: 'warning'
      }
      return tags[type] || 'info'
    },

    getQuestionTypeText(type) {
      const texts = {
        single_choice: '单选题',
        multiple_choice: '多选题',
        true_false: '判断题',
        subjective: '主观题'
      }
      return texts[type] || type
    },

    // 修改选项操作方法
    addOption() {
      this.form.options.push({ content: '', isCorrect: false });
      this.updateAnswerByOptions();
    },

    removeOption(index) {
      this.form.options.splice(index, 1);
      this.updateAnswerByOptions();
    },

    handleUploadSuccess(response, file, fileList) {
      this.form.attachments = fileList.map(file => file.response || file)
    },

    handleUploadRemove(file, fileList) {
      this.form.attachments = fileList
    },

    updateAnswerByOptions() {
      // 只处理选择题
      if (!['single_choice', 'multiple_choice'].includes(this.form.type)) return;
      const correctIndexes = this.form.options
        .map((opt, idx) => opt.isCorrect ? idx : -1)
        .filter(idx => idx !== -1);
      // 转换为大写字母
      const answer = correctIndexes.map(idx => String.fromCharCode(65 + idx)).join(
        this.form.type === 'multiple_choice' ? ',' : ''
      );
      this.form.answer = answer;
    },

    /*
    * 处理知识点选择变化
    */
    handleKnowledgePointsChange(selectedKnowledgePoints) {
      if (!selectedKnowledgePoints || selectedKnowledgePoints.length === 0) {
        this.form.chapter = []
        this.selectedKnowledgePointsDesc = []
        return
      }

      // 从 knowledgePointOptions 中找到选中知识点的完整信息
      const selectedKnowledgePointsInfo = selectedKnowledgePoints.map(id =>
        this.knowledgePointOptions.find(kp => kp.id === id)
      ).filter(kp => kp);

      // 更新知识点描述
      this.selectedKnowledgePointsDesc = selectedKnowledgePointsInfo.map(kp => ({
        name: kp.name,
        description: kp.description
      }));

      // 获取所有选中知识点的关联章节
      const promises = selectedKnowledgePointsInfo.map(knowledgePoint => {
        return getRelationData('knowledgepoint', 'chapters', knowledgePoint.documentId, {
          pageSize: 15,
          page: 1
        }).then(response => response.results || [])
      })

      // 等待所有请求完成
      Promise.all(promises)
        .then(chaptersArrays => {
          // 合并所有章节，去重
          const allChapters = chaptersArrays.flat()
          const uniqueChapterIds = [...new Set(allChapters.map(chapter => chapter.id))]

          // 更新章节选择
          this.form.chapter = uniqueChapterIds
        })
        .catch(error => {
          console.error('获取知识点关联章节失败:', error)
          this.$message.error('获取关联章节失败')
        })
    },


    switchAIBtn(model) {

      this.modelName = "gemini-2.0-flash"
      switch (model) {
        case "2.0flash":
          this.modelName = "gemini-2.0-flash"
          break;
        case "2.5pro":
          this.modelName = "gemini-2.5-pro-preview-03-25"
          break;
        case "2.5flash":
          this.modelName = "gemini-2.5-flash-preview-04-17"
          break;
        default:
          break;
      }

      // this.handleAIGenerate()
      this.handleAIModelGenerate()

    },
    /*
    * 处理 AI 生成按钮点击
    */
    handleAIGenerate() {
      // 获取课程名称
      const course = this.courseOptions.find(item => item.id === this.form.course)
      const courseName = course ? course.name : ''

      // 获取知识点信息
      const knowledgePoints = this.form.knowledgepoints.map(id =>
        this.knowledgePointOptions.find(kp => kp.id === id)
      ).filter(kp => kp)

      // 获取章节信息
      const chapters = this.form.chapter.map(id =>
        this.chapterOptions.find(ch => ch.id === id)
      ).filter(ch => ch)

      // 构建输出内容
      const output = {
        courseName: courseName,
        knowledgePoints: knowledgePoints.map(kp => `${kp.name}：\n${kp.description || '暂无描述'}`).join('\n\n'),
        chapters: chapters.map(ch => ch.name).join('、'),
        questionType: this.getQuestionTypeText(this.form.type)
      }

      // 在控制台输出
      console.log('AI 生成所需信息：', output)
      this.questionData = output
      // this.generateQuestion() // gemini
      this.handleAIModelGenerate() // gemini

      // this.createQuestionByCoze() // coze 豆包lite 128k
    },
    async generateQuestion(model) {
      this.btn2Loading = true;
      try {
        this.prompt = `你是一个专业的考试命题专家。请根据以下信息，生成一道符合考试标准的${this.questionData.questionType}。
生成要求如下：
1. 题目使用**标准中文**表达。
2. 根据题型不同，输出不同的JSON格式：
【单选题、多选题】格式：
{
  "stem": "题目内容",
  "options": [
    {"content": "选项内容", "isCorrect": false},
    {"content": "选项内容", "isCorrect": true},
    {"content": "选项内容", "isCorrect": false},
    {"content": "选项内容", "isCorrect": false}
  ],
  "answer": "正确选项的大写字母（如A、B、C、D）",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

【判断题】格式：
{
  "stem": "题目内容",
  "answer": "正确答案，True或False",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

【主观题】格式：
{
  "stem": "题目内容",
  "answer": "参考答案",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

3. 不要输出任何多余的文字，只输出符合要求的标准JSON。
4. 选项内容要合理、有干扰性（如果有选项的话）。
5. 答案解析要简洁明了。

参考的信息如下：

课程名：${this.questionData.courseName}
章节：${this.questionData.chapters}
知识点内容：
${this.questionData.knowledgePoints}`
        // 构建出题指令
        const systemInstruction = {
          parts: [{
            text: this.prompt
          }]
        };

        // 构建请求内容
        const requestData = {
          system_instruction: systemInstruction,
          contents: [{
            parts: [{
              text: ""
            }]
          }]
        };

        // 调用Gemini API
        const response = await axios.post(
          `https://generativelanguage.googleapis.com/v1beta/models/${this.modelName}:generateContent?key=AIzaSyDKSuAGKwo_wlt-Q-LcSTY7rJO8ysMh6Ho`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json'
            },
            responseType: 'json'
          }
        );

        // 按钮结束加载
        this.btn2Loading = false;

        const resAIJson = response.data.candidates[0].content.parts[0].text
        console.log(resAIJson);

        // 清理 JSON 字符串中的 Markdown 标记
        const cleanJson = resAIJson.replace(/```json\n?|\n?```/g, '').trim();
        console.log('清理后的 JSON:', cleanJson);

        // 解析清理后的 JSON 字符串
        const generatedQuestion = JSON.parse(cleanJson);

        // 填充表单数据
        this.form.stem = generatedQuestion.stem;
        this.form.options = generatedQuestion.options;
        this.form.answer = generatedQuestion.answer;
        this.form.explanation = generatedQuestion.explanation;
        this.form.difficulty = generatedQuestion.difficulty;

        // 更新答案显示
        this.updateAnswerByOptions();

        // 提示用户生成成功
        this.$message.success('题目生成成功');

        // 在成功生成题目后显示表单
        this.showDetailForm = true;

      } catch (error) {
        console.error('出题失败:', error);
        this.$message.error(error.message || '出题失败，请重试');
        this.btn2Loading = false;
      }
    },

    /*
    * 显示详细表单
    */
    handleShowDetail() {
      this.showDetailForm = true
    },

    /*
    * 获取 Coze Token
    */
    async getCozeToken() {
      try {
        const response = await axios.get('https://rest.bwaiwork.xyz/api/getcozetokentest');
        return response.data;
      } catch (error) {
        console.error('获取 Coze Token 失败:', error);
        throw error;
      }
    },

    /*
    * 使用 Coze 创建问题
    */
    async createQuestionByCoze() {
      this.btn2Loading = true;
      try {
        // 获取课程名称和其他信息（如果还没有）
        if (!this.questionData.courseName) {
          await this.handleAIGenerate();
        }

        // 获取token
        const tokenResponse = await this.getCozeToken();
        console.log('完整的token响应:', tokenResponse);

        // 更严格地提取token
        let token = tokenResponse.access_token

        console.log('提取的token:', token);

        // 验证token
        if (!token || typeof token !== 'string') {
          throw new Error('无法获取有效的token');
        }

        // 准备请求数据
        const requestData = {
          parameters: {
            questionData: {
              courseName: this.questionData.courseName,
              knowledgePoints: this.questionData.knowledgePoints,
              chapters: this.questionData.chapters,
              questionType: this.questionData.questionType
            }
          },
          workflow_id: "7498196794435862579"
        };

        console.log('发送请求前的token:', token);
        console.log('请求数据:', requestData);

        // 调用Coze API
        const response = await axios({
          method: 'post',
          url: 'https://api.coze.cn/v1/workflow/run',
          data: requestData,
          headers: {
            'Authorization': `Bearer ${token.trim()}`, // 确保没有多余空格
            'Content-Type': 'application/json'
          },
          timeout: 60000
        });

        console.log('API响应:', response);

        // 处理响应
        if (response.data && response.data.code === 0) {
          try {
            // 解析 data 字段中的 JSON 字符串
            const parsedData = JSON.parse(response.data.data);
            console.log('解析后的数据:', parsedData);

            // 检查是否有输出内容
            if (parsedData.output) {
              // 处理输出内容
              let outputContent = parsedData.output;

              // 如果输出包含题型标记，提取 JSON 部分
              const jsonMatch = outputContent.match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                outputContent = jsonMatch[0];
              }

              try {
                const generatedQuestion = JSON.parse(outputContent);
                console.log('生成的问题:', generatedQuestion);

                // 填充表单数据
                this.form.stem = generatedQuestion.stem;
                this.form.options = generatedQuestion.options;
                this.form.answer = generatedQuestion.answer;
                this.form.explanation = generatedQuestion.explanation;
                this.form.difficulty = generatedQuestion.difficulty;

                this.updateAnswerByOptions();
                this.showDetailForm = true;
                this.$message.success('题目生成成功');
              } catch (parseError) {
                // 如果输出不是 JSON 格式，显示原始输出
                console.error('解析输出内容失败:', parseError);
                this.$message.warning(parsedData.output);
              }
            } else {
              throw new Error('API返回数据中没有输出内容');
            }
          } catch (parseError) {
            console.error('解析响应数据失败:', parseError);
            this.$message.error('解析响应数据失败');
          }
        } else {
          throw new Error(response.data?.msg || 'API返回错误');
        }
      } catch (error) {
        console.error('Coze出题失败:', error);
        // 更详细的错误信息
        if (error.response) {
          console.error('错误状态码:', error.response.status);
          console.error('错误详情:', error.response.data);
          this.$message.error(`请求失败: ${error.response.status} - ${error.response.data?.message || '未知错误'}`);
        } else {
          this.$message.error(error.message || 'Coze出题失败，请重试');
        }
      } finally {
        this.btn2Loading = false;
      }
    },

    /**
   * 使用 requestAIModel 调用 AI 生成试题
   */
    async handleAIModelGenerate(provider, model) {
      // 获取课程名称
      const course = this.courseOptions.find(item => item.id === this.form.course)
      const courseName = course ? course.name : ''

      // 获取知识点信息
      const knowledgePoints = this.form.knowledgepoints.map(id =>
        this.knowledgePointOptions.find(kp => kp.id === id)
      ).filter(kp => kp)

      // 获取章节信息
      const chapters = this.form.chapter.map(id =>
        this.chapterOptions.find(ch => ch.id === id)
      ).filter(ch => ch)

      // 构建 prompt
      const prompt = `你是一个专业的考试命题专家。请根据以下信息，生成一道符合考试标准的${this.getQuestionTypeText(this.form.type)}。
生成要求如下：
1. 题目使用**标准中文**表达。
2. 根据题型不同，输出不同的JSON格式：
【单选题、多选题】格式：
{
  "stem": "题目内容",
  "options": [
    {"content": "选项内容", "isCorrect": false},
    {"content": "选项内容", "isCorrect": true},
    {"content": "选项内容", "isCorrect": false},
    {"content": "选项内容", "isCorrect": false}
  ],
  "answer": "正确选项的大写字母（如A、B、C、D）",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

【判断题】格式：
{
  "stem": "题目内容",
  "answer": "正确答案，True或False",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

【主观题】格式：
{
  "stem": "题目内容",
  "answer": "参考答案",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

3. 不要输出任何多余的文字，只输出符合要求的标准JSON。
4. 选项内容要合理、有干扰性（如果有选项的话）。
5. 答案解析要简洁明了。

参考的信息如下：

课程名：${courseName}
章节：${chapters.map(ch => ch.name).join('、')}
知识点内容：
${knowledgePoints.map(kp => `${kp.name}：\n${kp.description || '暂无描述'}`).join('\n\n')}
`

      this.btn2Loading = true
      try {
        // 调用通用AI接口
        const res = await requestAIModel({
          prompt,
          provider: provider || 'gemini', // 可根据需要切换
          model: model || 'f2.0' // 可选
        })

        // 1. 取出 OpenAI 格式的 content
        let aiText = res?.choices?.[0]?.message?.content
        if (!aiText) {
          throw new Error('AI返回内容为空')
        }
        console.log(aiText);


        // 2. 清理 JSON 字符串中的 Markdown 标记
        let cleanJson
        if (typeof aiText === 'object') {
          cleanJson = aiText
        } else {
          let cleanStr = aiText
          if (/^```json/.test(cleanStr)) {
            cleanStr = cleanStr.replace(/```json\s*|\s*```/g, '').trim()
          }
          cleanJson = JSON.parse(cleanStr)
        }

        // 填充表单
        this.form.stem = cleanJson.stem
        this.form.options = cleanJson.options
        this.form.answer = cleanJson.answer
        this.form.explanation = cleanJson.explanation
        this.form.difficulty = cleanJson.difficulty

        this.updateAnswerByOptions()
        this.showDetailForm = true
        this.$message.success('题目生成成功')
      } catch (error) {
        this.$message.error(error.message || 'AI生成失败')
      } finally {
        this.btn2Loading = false
      }
    },
    
    handleBatchClick(){
      this.batchDialogVisible = true
    },
    async handleBatchImport() {
      // 处理批量导入逻辑
      console.log('批量导入内容:', this.batchInput);
      // 这里可以根据需要进行进一步的处理，比如将输入的内容解析为多个试题对象
       // 构建 prompt
       const prompt = `请帮我将以下文本转换为json格式：${this.batchInput}。
生成格式如下：
1.根据题型不同，输出不同的JSON格式：
【单选题、多选题】格式：
{
  "stem": "题目内容",
  "options": [
    {"content": "选项内容", "isCorrect": false},
    {"content": "选项内容", "isCorrect": true},
    {"content": "选项内容", "isCorrect": false},
    {"content": "选项内容", "isCorrect": false}
  ],
  "answer": "正确选项的大写字母（如A、B、C、D）",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

【判断题】格式：
{
  "stem": "题目内容",
  "answer": "正确答案，True或False",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

【主观题】格式：
{
  "stem": "题目内容",
  "answer": "参考答案",
  "explanation": "答案解析说明",
  "difficulty": 题目整体难度，整数（1到5之间）
}

3. 不要输出任何多余的文字，只输出符合要求的标准JSON。
4. 选项内容要合理、有干扰性（如果有选项的话）。
5. 答案解析要简洁明了。

`

      this.btn2Loading = true
      try {
        // 调用通用AI接口
        const res = await requestAIModel({
          prompt,
          provider: 'gemini', // 可根据需要切换
          model: 'f2.0' // 可选
        })

        // 1. 取出 OpenAI 格式的 content
        let aiText = res?.choices?.[0]?.message?.content
        if (!aiText) {
          throw new Error('AI返回内容为空')
        }
        console.log(aiText);


        // 2. 清理 JSON 字符串中的 Markdown 标记
        let cleanJson
        if (typeof aiText === 'object') {
          cleanJson = aiText
        } else {
          let cleanStr = aiText
          if (/^```json/.test(cleanStr)) {
            cleanStr = cleanStr.replace(/```json\s*|\s*```/g, '').trim()
          }
          cleanJson = JSON.parse(cleanStr)
        }

        console.log(cleanJson);

        this.batchQuestions = cleanJson
        this.batchSelectDialogVisible = true

        // // 填充表单
        // this.form.stem = cleanJson.stem
        // this.form.options = cleanJson.options
        // this.form.answer = cleanJson.answer
        // this.form.explanation = cleanJson.explanation
        // this.form.difficulty = cleanJson.difficulty

        // this.updateAnswerByOptions()
        // this.showDetailForm = true
        this.$message.success('题目生成成功')
      } catch (error) {
        this.$message.error(error.message || 'AI生成失败')
      } finally {
        this.btn2Loading = false
      }

      // this.batchDialogVisible = false;
    },
    handleBatchSelectionChange(selection) {
      this.batchSelectedQuestions = selection
    },
    async handleBatchAddConfirm() {
      if (!this.batchSelectedQuestions.length) {
        this.$message.warning('请至少选择一道试题')
        return
      }
      this.btnLoading = true
      try {
        // 取批量弹框中选择的课程、知识点、章节
        const course = this.form.course
        const knowledgepoints = this.form.knowledgepoints
        const chapter = this.form.chapter
        // 构建 chapters/knowledgepoints 的 connect 格式
        const knowledgepointsConnect = (knowledgepoints || []).map(id => {
          const kp = this.knowledgePointOptions.find(item => item.id === id)
          return kp ? { id: kp.id, documentId: kp.documentId } : null
        }).filter(Boolean)
        const chaptersConnect = (chapter || []).map(id => {
          const ch = this.chapterOptions.find(item => item.id === id)
          return ch ? { id: ch.id, documentId: ch.documentId } : null
        }).filter(Boolean)

        for (const q of this.batchSelectedQuestions) {
          // 补全字段
          const data = {
            ...q,
            type: 'single_choice',
            course: course,
            knowledgepoints: knowledgepoints,
            chapter: chapter,
            tag: [],
            attachments: [],
            chapters: {
              connect: chaptersConnect,
              disconnect: []
            },
            knowledgepoints: {
              connect: knowledgepointsConnect,
              disconnect: []
            },
            tags: {
              connect: [],
              disconnect: []
            }
          }
          await postData(this.apiName, 'post', '', data)
        }
        this.$message.success('批量新增成功')
        this.getList(this.listQuery)
        this.batchSelectDialogVisible = false
        this.batchDialogVisible = false
      } catch (e) {
        this.$message.error('批量新增失败')
      } finally {
        this.btnLoading = false
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}

.description-text {
  display: inline-block;
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-upload {
  width: 100%;
}

.description-text {
  max-width: 250px;
}

.input-tip {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #909399;
  font-size: 12px;
  
  .word-count {
    color: #409EFF;
  }
  
  .tip-text {
    color: #909399;
  }
}

.question-preview {
  .question-stem {
    margin-bottom: 10px;
    font-weight: 500;
  }

  .question-options {
    .option-item {
      margin-bottom: 5px;
      padding: 5px 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}
</style>
