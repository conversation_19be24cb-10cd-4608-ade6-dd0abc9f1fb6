<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1> 郵件模板 </h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>

      <el-table-column prop="name" label="名稱" width="" />
      <el-table-column prop="lang" label="語言" width="" />
      <el-table-column prop="text" label="郵件模板內容" width="560">
        <template slot-scope="{row}">
          <el-popover placement="top" width="420" trigger="click">
            <div class="describe-tooltip" style="white-space: pre-wrap;">{{ row.text }}</div>
            <el-button class="describe" slot="reference" type="text">{{ row.text }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>

          <el-button @click="showLangSelect(scope.row)" style="margin-left:12px">AI生成模板</el-button>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-form-item label="名稱（英文）" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item label="語言" prop="lang">
          <el-select v-model="form.lang" class="filter-item" placeholder="Please select">
            <el-option v-for="item in langOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="郵件模板內容" prop="text">
          <el-input type="textarea" :autosize="true" v-model="form.text" />
          <div v-if="isTyping" class="typing-effect">
            {{ typingText }}
            <span class="cursor">|</span>
          </div>
        </el-form-item>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary"
          @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>

      </div>
    </el-dialog>
    <el-dialog width="80%" title="選點" :visible.sync="dialogMapVisible">
      <!-- <h2>{{ modelId }}</h2> -->
      <div>
        <el-button @click="dialogMapVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="modelFunc">
          確認
        </el-button>
      </div>
    </el-dialog>

    <!-- 添加语言选择弹框 -->
    <el-dialog title="选择语言" :visible.sync="langDialogVisible" width="30%">
      <el-form>
        <el-form-item label="选择语言">
          <el-select v-model="selectedLang" placeholder="请选择语言">
            <el-option v-for="item in langOptions" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="langDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmLangSelect" :loading="btn2Loading">确认</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import axios from "axios"
import { getData, postData, uploadData } from '@/api/requestData'
import Pagination from '@/components/Pagination'
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

export default {
  name: 'Product',
  components: { Pagination, ImageUploader },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: "template", // 变量修改，请求主题名称
      listLoading: false,
      btnLoading: false,
      btn2Loading: false,

      modelId: "",
      // 弹框
      dialogFormVisible: false,
      // 地圖彈框
      dialogMapVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },
      statusOptions: [],
      langOptions: ["En", "繁體中文", "简体中文", "泰文"],
      // 楼层选项
      floorOptions: [
        {
          value: "1",
          label: "GF"
        }, {
          value: "2",
          label: "L1"
        }, {
          value: "3",
          label: "L2"
        }, {
          value: "4",
          label: "L3"
        },
      ],

      resetTrigger: 0, // 控制重置逻辑的状态变量 ImageUploader
      // 变量修改，表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        lang: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        text: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ]
      },
      langDialogVisible: false, // 新增语言选择弹框显示状态
      selectedLang: '', // 新增选中的语言
      selectedRow: null, // 新增选中的行数据
      isTyping: false,
      typingText: '',
      typingSpeed: 8, // 打字速度（毫秒）
    }
  },
  created() {
    this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    validateLetter(rule, value, callback) {
      if (/^[A-Za-z]{1}$/.test(value)) {
        callback();
      } else {
        callback(new Error('請輸入單個英文字母'));
      }
    },

    /*
    上傳文件前的处理 ImageUploader
    */
    handleUploadSuccess({ id, type }) {
      // 使用传递的type动态更新相应的状态
      console.log(`${type}:`, id);
      this.form[`${type}`] = id;
      console.log(this.form);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    /*
    點擊修改的事件
    */
    handleClick(row) {
      // this.listLoading = true

      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      // 变量修改时的参数
      this.formRow = {
        id: row.id,
        name: row.name,
        lang: row.lang,
        text: row.text,

        // img: row.img.url,
        // description: row.description,
        // description_tc: row.description_tc,
        // description_zh: row.description_zh,
      }

      this.form = Object.assign({}, this.formRow) // copy obj
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let form = this.form


          if (this.fileId) {
            // 设定pdf的id
            this.form.img = parseInt(this.fileId)

          }

          const tempData = Object.assign({}, form)
          console.log(tempData)
          postData(this.apiName, 'put', tempData.id, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    清空提交表
    */
    resetForm() {

      // 变量修改，清零配置
      this.form = {
        name: "",
        lang: "",
        text: "",

      }
      // 文件上傳部分的清零
      this.file = ""
      this.fileId = ""
      this.fileName = ""
      this.fileUploadType = false
      // 清空組件狀態 ImageUploader
      this.triggerReset()
    },
    // ImageUploader 重置器
    triggerReset() {
      this.resetTrigger += 1; // 每次调用时改变值，触发ImageUploader的重置
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log("form")
        console.log(this.form.text)
        console.log(typeof this.form)
        console.log(valid)
        if (valid) {
          if (this.form.text) {

            const temp = this.form
            const tempData = Object.assign({}, temp)
            console.log("tempData")
            console.log(tempData)
            // 按鈕開始加載
            this.btnLoading = true
            postData(this.apiName, 'post', '', tempData).then(() => {
              this.getList(this.listQuery)
              // 按鈕停止加載並關閉彈框
              this.btnLoading = false
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Create Successfully',
                type: 'success',
                duration: 1000
              })
            })
          } else {
            this.$notify({
              title: 'Warning',
              message: '請先填写模板内容',
              type: 'success',
              duration: 1000
            })

          }
        }
      })
    },

    /*
    管理關聯表表的方法
    */
    connectFunc(newId, oldId) {
      if (newId) {
        if (oldId) {
          if (oldId == newId) {
            return {
              "disconnect": [],
              "connect": []
            }
          } else {

            return {
              "disconnect": [
                {
                  "id": parseInt(oldId)
                }],
              "connect": [
                {
                  "id": parseInt(newId),
                  "position": {
                    "end": true
                  }
                }
              ]
            }
          }

        } else {
          return {
            "disconnect": [],
            "connect": [
              {
                "id": parseInt(newId),
                "position": {
                  "end": true
                }
              }
            ]
          }
        }

      } else if (oldId) {
        return {
          "disconnect": [
            {
              "id": parseInt(oldId),
              "position": {
                "end": true
              }
            }],
          "connect": []
        }
      }
      else {
        return {
          "disconnect": [],
          "connect": []
        }
      }


    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.apiName, "DELETE", row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    // 获取首字母的方法
    generateInitials() {
      const name = this.form.name.trim();
      if (name === '') {
        this.form.initials = '';
      } else if (/^[A-Za-z]/.test(name)) {
        this.form.initials = name.charAt(0).toUpperCase();
      } else {
        this.form.initials = '1';
      }
    },

    // map的数据返回
    handleValueChanged(value) {
      this.modelId = value;
    },
    // 賦值modelId的方法
    modelFunc() {
      this.dialogMapVisible = false
      this.form.modelId = this.modelId
    },
    // 显示语言选择弹框
    showLangSelect(row) {
      this.selectedRow = row;
      this.langDialogVisible = true;
    },

    // 打字机效果
    async typeText(text) {
      this.isTyping = true;
      this.typingText = '';

      for (let i = 0; i < text.length; i++) {
        this.typingText += text[i];
        await new Promise(resolve => setTimeout(resolve, this.typingSpeed));
      }

      this.isTyping = false;
      this.form.text = text;
    },

    // 确认语言选择
    async confirmLangSelect() {
      if (!this.selectedLang) {
        this.$message.warning('请选择语言');
        return;
      }

      // 按鈕開始加載
      this.btn2Loading = true
      try {
        // 构建翻译指令
        const systemInstruction = {
          parts: [{
            text: `You are a professional email translator. You only translate and do not explain. Please help me translate the following content into ${this.selectedLang}`
          }]
        };

        // 构建请求内容
        const requestData = {
          system_instruction: systemInstruction,
          contents: [{
            parts: [{
              text: this.selectedRow.text
            }]
          }]
        };

        // 调用Gemini API
        const response = await axios.post(
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?alt=sse&key=AIzaSyDKSuAGKwo_wlt-Q-LcSTY7rJO8ysMh6Ho',
          requestData,
          {
            headers: {
              'Content-Type': 'application/json'
            },
            responseType: 'text'
          }
        );

        // 按鈕開始加載
        this.btn2Loading = false

        // 解析SSE格式的响应
        const lines = response.data.split('\n');
        let translatedText = '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonData = JSON.parse(line.substring(6));
              if (jsonData.candidates && jsonData.candidates[0] && jsonData.candidates[0].content) {
                translatedText = jsonData.candidates[0].content.parts[0].text;
                break;
              }
            } catch (e) {
              console.log('解析行失败:', line);
            }
          }
        }

        if (!translatedText) {
          throw new Error('No translation result found');
        }

        // 打开编辑弹框
        this.dialogStatus = 'create';
        this.dialogFormVisible = true;
        this.form = {
          name: this.selectedRow.name + `(${this.selectedLang})`,
          lang: this.selectedLang,
          text: '' // 初始为空，等待打字机效果
        };

        // 关闭语言选择弹框
        this.langDialogVisible = false;
        this.selectedLang = '';

        // 开始打字机效果
        await this.typeText(translatedText);
      } catch (error) {
        console.error('Translation error:', error);
        this.$message.error(error.message || '翻译失败，请重试');
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}

.typing-effect {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 100px;
  white-space: pre-wrap;
  line-height: 1.5;
}

.cursor {
  animation: blink 1s infinite;
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}
</style>
