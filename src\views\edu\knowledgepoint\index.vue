<template>
  <div class="app-container">
    <h1>知识点</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="知识点名称" width="180" />
      <el-table-column prop="description" label="说明" width="200">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.description" :content="scope.row.description" placement="top">
            <span class="description-text">{{ scope.row.description }}</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" label="考点级别" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.level === 1 ? 'primary' : 'success'">
            {{ scope.row.level === 1 ? '一级考点' : '二级考点' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="course.name" label="所属课程" width="150" />
      <el-table-column prop="parentKnowledgePoint.name" label="父知识点" width="150">
        <template slot-scope="scope">
          {{ scope.row.parentKnowledgePoint ? scope.row.parentKnowledgePoint.name : '无' }}
        </template>
      </el-table-column>
      <el-table-column label="关联数据" width="300">
        <template slot-scope="scope">
          <el-tag size="small" type="info">子知识点: {{(scope.row.childKnowledgePoints.count ) || 0}}</el-tag>
          <el-tag size="small" type="success">章节: {{(scope.row.chapters.count ) || 0}}</el-tag>
          <el-tag size="small" type="warning">试题: {{(scope.row.questions.count) || 0}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 600px; margin-left:50px;">

        <el-form-item label="知识点名称" prop="name">
          <el-input v-model="form.name" placeholder="如：函数的定义" />
        </el-form-item>

        <el-form-item label="考点级别" prop="level">
          <el-select v-model="form.level" placeholder="请选择考点级别" style="width: 100%" clearable>
            <el-option :value="1" label="一级考点" />
            <el-option :value="2" label="二级考点" />
          </el-select>
        </el-form-item>

        <el-form-item label="所属课程" prop="course">
          <el-select 
            v-model="form.course" 
            placeholder="请选择课程" 
            style="width: 100%"
            filterable
            clearable>
            <el-option
              v-for="item in courseOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="父知识点" prop="parentKnowledgePoint">
          <el-select 
            v-model="form.parentKnowledgePoint" 
            placeholder="请选择父知识点（可选）" 
            style="width: 100%"
            filterable
            clearable
            :disabled="!form.course">
            <el-option
              v-for="item in knowledgePointOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关联章节" prop="chapters">
          <el-select 
            v-model="form.chapters" 
            multiple 
            placeholder="请选择关联章节" 
            style="width: 100%"
            filterable
            clearable
            collapse-tags
            :disabled="!form.course">
            <el-option
              v-for="item in chapterOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="详细说明" prop="description">
          <el-input type="textarea" v-model="form.description" :rows="4" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData, uploadData, getRelationData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'
import relationHandler from '@/mixins/relationHandler'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      baseURL: 'http://localhost:1338',
      apiName: "knowledgepoint",
      listLoading: false,
      btnLoading: false,
      fileUploadType: false,
      // 弹框
      dialogFormVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {
        name: '',
        description: '',
        level: null,
        course: null,
        parentKnowledgePoint: null,
        chapters: []
      },
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },
      courseOptions: [], // 课程选项
      chapterOptions: [], // 章节选项
      knowledgePointOptions: [], // 知识点选项（用于选择父知识点）
      // 变量修改，表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入知识点名称', trigger: 'blur' }
        ],
        course: [
          { required: true, message: '请选择所属课程', trigger: 'change' }
        ]
      },
      prevChapters: [], // 记录编辑前的章节id
    }
  },
  created() {
    this.getList(this.listQuery)
    this.getCourses()
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  watch: {
    // 监听课程选择变化
    'form.course'(newVal) {
      if (newVal) {
        this.getChaptersByCourse(newVal)
        this.getKnowledgePointsByCourse(newVal)
      } else {
        this.chapterOptions = []
        this.knowledgePointOptions = []
        this.form.parentKnowledgePoint = null
        this.form.chapters = []
      }
    }
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    /*
    获取课程列表
    */
    getCourses() {
      getData('course').then(response => {
        this.courseOptions = response.results
      })
    },

    /*
    获取指定课程的章节列表
    */
    getChaptersByCourse(courseId) {
      getData('chapter', { course: courseId }).then(response => {
        this.chapterOptions = response.results
      })
    },

    /*
    获取知识点关联的章节列表
    */
    getChaptersByKnowledgePoint(knowledgePointId, documentId) {
      
      return getRelationData('knowledgepoint', 'chapters', documentId, {
        pageSize: 100 // 获取所有关联的章节
      }).then(response => {
        return response.results || []
      }).catch(error => {
        console.error('获取章节关联失败:', error)
        return []
      })
    },

    /*
    获取指定课程的知识点列表（用于选择父知识点）
    */
    getKnowledgePointsByCourse(courseId) {
      getData('knowledgepoint', { course: courseId }).then(response => {
        // 过滤掉当前正在编辑的知识点
        this.knowledgePointOptions = response.results.filter(item => 
          item.id !== this.form.id
        )
      })
    },

    /*
    點擊修改的事件
    */
    handleClick(row) {
      console.log('编辑行数据:', row)
      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      
      this.formRow = {
        id: row.id,
        documentId: row.documentId,
        name: row.name,
        description: row.description || '',
        level: row.level || null,
        course: row.course ? row.course.id : null,
        parentKnowledgePoint: row.parentKnowledgePoint ? row.parentKnowledgePoint.id : null,
        chapters: [], 
        childKnowledgePoints: [],
        questions: []
      }
      
      this.form = Object.assign({}, this.formRow)
      
      if (this.form.course) {
        this.getChaptersByCourse(this.form.course)
        this.getKnowledgePointsByCourse(this.form.course)
        
        this.getChaptersByKnowledgePoint(row.id, row.documentId).then(chapters => {
          console.log(chapters);
          
          this.form.chapters = chapters.map(item => item.id)
          // 使用 mixin 方法初始化关联字段
          this.initRelationField('chapters', this.form.chapters)
        })
      }
    },

    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.form)
          // 使用 mixin 方法构建关联关系
          tempData.chapters = this.buildRelation('chapters', this.form.chapters, this.chapterOptions)
          
          postData(this.apiName, 'put', tempData.documentId, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    修改创建按钮的处理方法
    */
    handleCreate() {
      this.dialogStatus = 'create'  // 先设置状态
      this.resetForm()              // 再重置表单
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    重置表单方法保持不变
    */
    resetForm() {
      this.form = {
        name: '',
        description: '',
        level: null,
        course: null,
        parentKnowledgePoint: null,
        chapters: [],
        childKnowledgePoints: [],
        questions: []
      }
      // 使用 mixin 方法重置关联字段
      this.resetRelationFields(['chapters'])
      this.chapterOptions = []
      this.knowledgePointOptions = []
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.form)
          // 使用 mixin 方法构建创建时的关联关系
          tempData.chapters = this.buildCreateRelation('chapters', this.form.chapters, this.chapterOptions)
          
          this.btnLoading = true
          postData(this.apiName, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            this.btnLoading = false
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },

    // 删除的最后确认按钮
    deleteDialog(row) {
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        postData(this.apiName, "DELETE", row.documentId, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

  },
  mixins: [relationHandler],
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}

.description-text {
  display: inline-block;
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
