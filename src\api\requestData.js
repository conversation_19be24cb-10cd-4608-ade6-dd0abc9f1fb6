import request from '@/utils/request'

import axios from 'axios';


// 通用方法
// 獲取数据
export function getData(baseUrl, params) {
  return request({
    url: `/content-manager/collection-types/api::${baseUrl}.${baseUrl}/`,
    method: 'get',
    params
  })
}

// 獲取数据
export function getDataOne(baseUrl, params, num) {
  return request({
    url: `/content-manager/collection-types/api::${baseUrl}.${baseUrl}/` + num,
    method: 'get',
    params
  })
}

// 新增、修改、删除数据
export function postData(baseUrl, type, num, data) {
  return request({
    url: `/content-manager/collection-types/api::${baseUrl}.${baseUrl}/` + num,
    method: type,
    data
  })
}
// 新增、修改、删除数据 單條模式
export function postSingleData(baseUrl, type, num, data) {
  return request({
    url: `/content-manager/single-types/api::${baseUrl}.${baseUrl}/` + num,
    method: type,
    data
  })
}


// 上传文件
export function uploadData(data) {
  return request({
    url: `/upload`,
    method: "POST",
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 获取关联关系数据
export function getRelationData(baseUrl, relationField, id, params = {}) {
  return request({
    url: `/content-manager/relations/api::${baseUrl}.${baseUrl}/${id}/${relationField}`,
    
    method: 'get',
    params: {
      id,
      ...params
    }
  })
}


/**
 * 通用AI模型请求方法
 * @param {Object} params { prompt, provider, model }
 * @returns Promise<any>
 */
export async function requestAIModel({ prompt, provider = 'gemini', model = '' }) {
  let defaultModel = 'f2.0'
  let usedProvider = provider.toLowerCase()
  switch (usedProvider) {
    case 'silicon':
      defaultModel = 'qwen3'
      break
    case 'openai':
      defaultModel = 'gpt-4o-mini'
      break
    case 'gemini':
    default:
      defaultModel = 'f2.0'
      usedProvider = 'gemini'
      break
  }
  const finalModel = model || defaultModel
  try {
    const res = await axios.post(
      'https://rest.bwaiwork.xyz/api/aichat',
      {
        prompt,
        provider: usedProvider,
        model: finalModel
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
    return res.data
  } catch (error) {
    // 可以根据需要自定义错误处理
    throw error
  }
}