<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1>{{ $t('route.notice') }}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">

      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="通告名(英文)" width="" />
      <el-table-column prop="name_tc" label="通告名(繁体)" width="" />
      <el-table-column prop="name_zh" label="通告名 (简体)" width="" />

      <el-table-column prop="machine" label="通告可見設備" width="">
        <template slot-scope="{row}">
          <el-tag type="warning" v-for="item of getMachineName(row.machine)">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="pdf_type" label="通告類型" width="">
        <template slot-scope="{row}">
          <el-tag>{{ row.pdf_type ? row.pdf_type.name_tc : "草稿" }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-button type="primary" size="small">
            <a :href='baseURL + scope.row.file.url' target="_blank">查看PDF</a>
          </el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-form-item label="通告名稱（英文）" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="通告名稱（繁體）" prop="name_tc">
          <el-input v-model="form.name_tc" />
        </el-form-item>
        <el-form-item label="通告名稱（簡體）" prop="name_zh">
          <el-input v-model="form.name_zh" />
        </el-form-item>

        <el-form-item label="通告可見設備" prop="machine">
          <div v-if="form.machine">
            <el-select v-model="form.machine" multiple placeholder="请選擇（不選為全可見）" :disabled="true">
              <el-option v-for="item in form.machine" :key="item.machineId" :label="item.name" :value="item.machineId">
              </el-option>
            </el-select>

            <el-button  @click="form.machine = ''"
              style="margin-left:10px">修改</el-button>
          </div>

          <div v-if="!form.machine">
            <el-select v-model="machine" multiple placeholder="请選擇（不選為全可見）">
              <el-option v-for="item in machineData" :key="item.machineId" :label="item.name" :value="item.machineId">
              </el-option>
            </el-select>
          </div>

        </el-form-item>

        <!-- <el-upload class="upload-demo" :action="`/upload`" :on-success="handleSuccess" :before-upload="handleBeforeUpload"
          :on-remove="handleRemove" :multiple="false" :show-file-list="false">
          <el-button size="small" type="primary">点击上传</el-button>
        </el-upload> -->

        <el-form-item label="文件" prop="file">
          <div v-loading="uploadLoading">
            <el-upload class="upload-demo" action="/upload/" :limit="1" :before-upload="handleBeforeUpload" accept=".pdf"
              :show-file-list="false">
              <el-button size="small" type="primary">選擇文件</el-button><span class="fileName">{{ fileName }}</span>

              <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
            </el-upload>
            <el-button size="small" @click="uploadFile" v-if="!fileUploadType && fileName"
              style="margin-top:10px">上傳文件</el-button>
            <el-tag type="success" v-if="fileUploadType">文件上傳成功</el-tag>

          </div>
        </el-form-item>

        <el-form-item :label="$t('table.status')" style="margin-top:10px">
          <el-select v-model="form.stateType" class="filter-item" placeholder="Please select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import axios from "axios"
import { getData, postData, uploadData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      machineData: [], //設備數據
      selectEdit: true,
      machine:"",
      total: 0,
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: "upload-file",
      listLoading: false,
      uploadLoading: false,
      btnLoading: false,
      file: "",
      fileName: "",
      fileId: "",
      fileUploadType: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '0',
        label: "草稿"
      }, {
        value: '1',
        label: "通告"
      }, {
        value: '2',
        label: "會所"
      }, {
        value: '3',
        label: "業委會資訊"
      }, {
        value: '4',
        label: "其他"
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },

      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_tc: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_zh: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    this.getMachine()
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    /*
    获取列表数据
    */
    getMachine() {
      getData("call", { "filters[$and][0][type][$eq]": "machine" }).then(response => {
        const res = response.results
        this.machineData = res
        console.log(this.machineData);
      })
    },
    /*
    通過數組返回對應的設備名稱
    */
    getMachineName(array) {
      let arr = JSON.parse(array)
      let names
      if (arr.length > 15) {
        names = ["ALL"]
      } else {
        names = arr.map((id) => {
          // 在data2数组中查找与当前id匹配的对象
          const item = this.machineData.find((obj) => obj.machineId == id);
          // 如果找到了对应的对象，则返回其name属性，否则返回null
          return item ? item.name : null;
        });
      }
      return names
    },
    /*
    通過數組重組設備方法，用於編輯顯示設備
    */
    getMachineType(array) {
      let arr = JSON.parse(array)
      let names
      if (arr.length > 15) {
        names = []
      } else {
        names = arr.map((id) => {
          // 在data2数组中查找与当前id匹配的对象
          const item = this.machineData.find((obj) => obj.machineId == id);
          // 如果找到了对应的对象，则返回其name属性，否则返回null
          return item ? { "machineId": parseInt(item.machineId), "name": item.name } : null;
        });
      }
      console.log(names);
      return names
    },
    /*
    上傳文件前的处理
    */
    handleBeforeUpload(file) {
      const acceptedFormats = /(application\/(pdf))/;
      const isType = acceptedFormats.test(file.type)
      if (!isType) {
        this.$message.error("只能上傳PDF");
      } else {
        this.file = file
        this.fileName = file.name
      }
    },
    uploadFile() {
      // 上傳加載中
      this.uploadLoading = true
      const formData = new FormData();
      formData.append('files', this.file);

      uploadData(formData).then(response => {
        // loading結束，設定已完成上傳，設定fileID
        this.uploadLoading = false
        this.fileUploadType = true
        this.fileId = response[0].id
        this.$notify({
          title: '上傳成功',
          message: `文件${this.fileName}`,
          type: 'success'
        })
      })
        .catch(error => {
          this.uploadLoading = false
          console.log('上传图片失败')
          this.$notify({
            title: '上傳失敗',
            message: `文件：${this.fileName}`,
            type: 'error'
          })
          console.log(error);
        });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    /*
    點擊修改的事件
    */
    handleClick(row) {
      // this.listLoading = true
      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      let pdf_type
      if (row.pdf_type) {
        pdf_type = {
          value: row.pdf_type.id + '',
          label: row.pdf_type.name_tc
        }
      } else {
        pdf_type = {
          value: '0',
          label: "草稿"
        }

      }
      this.fileName = row.file.name
      this.formRow = {
        id: row.id,
        name: row.name,
        name_tc: row.name_tc,
        name_zh: row.name_zh,
        machine: this.getMachineName(row.machine),
        file: row.file.id,
        stateType: pdf_type
      }
      console.log(this.formRow);
      this.form = Object.assign({}, this.formRow) // copy obj
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let form = this.form
          // console.log(this.formRow);
          // console.log(this.form);
          let pdf_type = this.connectFunc(parseInt(this.form.stateType.value), parseInt(this.formRow.stateType.value))

          console.log(this.form);
          this.form.pdf_type = pdf_type
          // 如果重新上传了图片，则设定新的pdf的id
          if (this.fileId) {
            this.form.file = parseInt(this.fileId)
          }
          // 如果有換設備，則重新設定machine
          if (this.machine[0]) {
            this.form.machine = JSON.stringify(this.machine)
          }else{
            this.form.machine = "[1,2, 3, 4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"
          }
          delete this.form.stateType
          const tempData = Object.assign({}, form)
          console.log(tempData)
          postData(this.apiName, 'put', tempData.id, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    清空提交表
    */
    resetForm() {
      this.form = {
        name: "",
        name_tc: "",
        name_zh: "",
        machine: "",
        file: "",
        stateType: {}
      }
      // 文件上傳部分的清零
      this.file = ""
      this.fileId = ""
      this.machine= ""
      this.fileName = ""
      this.fileUploadType = false
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      this.form.stateType = {
        value: '0',
        label: "草稿"
      }
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          // 如果状态为0，则为空，否则就有内容
          let pdf_type = this.connectFunc(parseInt(this.form.stateType.value), '')

          // 重设pdf_type
          this.form.pdf_type = pdf_type
          // 设定pdf的id
          this.form.file = parseInt(this.fileId)

          // 如果有換設備，則重新設定machine
          if (this.machine[0]) {
            this.form.machine = JSON.stringify(this.machine)
          }else{
            this.form.machine = "[1,2, 3, 4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"
          }
          if (this.form.file) {

            // 设定通告的类型
            delete this.form.stateType
            const temp = this.form
            const tempData = Object.assign({}, temp)
            console.log(tempData)
            // 按鈕開始加載
            this.btnLoading = true
            postData(this.apiName, 'post', '', tempData).then(() => {
              this.getList(this.listQuery)
              // 按鈕停止加載並關閉彈框
              this.btnLoading = false
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Create Successfully',
                type: 'success',
                duration: 1000
              })
            })
          } else {
            this.$notify({
              title: 'Warning',
              message: '請先上傳文件',
              type: 'success',
              duration: 1000
            })

          }
        }
      })
    },

    /*
    管理關聯表表的方法
    */
    connectFunc(newId, oldId) {
      if (newId) {
        if (oldId) {
          if (oldId == newId) {
            return {
              "disconnect": [],
              "connect": []
            }
          } else {

            return {
              "disconnect": [
                {
                  "id": parseInt(oldId)
                }],
              "connect": [
                {
                  "id": parseInt(newId),
                  "position": {
                    "end": true
                  }
                }
              ]
            }
          }

        } else {
          return {
            "disconnect": [],
            "connect": [
              {
                "id": parseInt(newId),
                "position": {
                  "end": true
                }
              }
            ]
          }
        }

      } else if (oldId) {
        return {
          "disconnect": [
            {
              "id": parseInt(oldId),
              "position": {
                "end": true
              }
            }],
          "connect": []
        }
      }
      else {
        return {
          "disconnect": [],
          "connect": []
        }
      }


    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.apiName, "DELETE", row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}
</style>
