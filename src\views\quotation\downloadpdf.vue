<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" class="main-article"
    element-loading-text="Efforts to generate PDF">
    <div class="article__heading">
      <div class="title-box">
        <img src="./print-logo.png" alt="">
        <div class="title">
          <h2>eShowcase Ltd.</h2>
          <p>Flat D03, 5/F, 33A-37A Tsuek Luk Street, Hong Kong</p>
          <p>Tel: 852-82000427</p>
        </div>
        <h1 style="text-transform: uppercase ">{{ printData.type }}<span v-if="printData.type === 'delivery'">
            NOTE</span>
        </h1>
        <!-- <h1 style="text-transform: uppercase ">{{printData.type}}</h1> -->
      </div>
      <div class="formdata">
        <div class="box left">
          <div class="col">
            <span class="name">Company:</span>
            <span>{{ printData.company }}
            </span>
          </div>
          <div class="col">
            <span class="name">Address:</span>
            <span>{{ printData.address }}</span>
          </div>
          <div class="col">
            <span class="name">Contact:</span>
            <span>{{ printData.contact_person }}</span>
          </div>
          <div class="col">
            <span class="name">Term:</span>
            <span>{{ printData.payment_name }}</span>
          </div>
        </div>
        <div class="box right">
          <div class="col">
            <span class="name" style="text-transform: capitalize ">{{ printData.type }} No.:</span>
            <span>{{ printData.uuid }}</span>
          </div>
          <div class="col">
            <span class="name">Project:</span>
            <span>{{ printData.name }}</span>
          </div>
          <div class="col">
            <span class="name">Quotation Date:</span>
            <span>{{ printData.updatedAt.substring(0, 10) }}</span>
          </div>
          <div class="col">
            <span class="name">Currency:</span>
            <span>{{ printData.currency }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="table">
      <div class="title">
        <span>Item</span>
        <span :class="[printData.type === 'delivery' ? 'delivery' : '']">Desciptions</span>
        <span>Qty</span>
        <span v-if="printData.type != 'delivery'">List Price</span>
        <span v-if="printData.type != 'delivery'">Total</span>
      </div>
      <div class="content" v-for="(item, index) of printData.content">
        <span>{{ index + 1 }}</span>
        <span class="desc" :class="[printData.type === 'delivery' ? 'delivery' : '']">
          <h4 class="name">{{ item.label }}</h4>
          <p style="white-space: pre-wrap;">{{ item.describe }}</p>
        </span>
        <span>{{ item.num | getArea }}</span>
        <span v-if="printData.type != 'delivery'">{{ item.price | getArea }}</span>
        <span v-if="printData.type != 'delivery'">{{ item.totalPrice | getArea }}</span>
      </div>

      <div class="total" v-if="printData.type != 'delivery'">
        <span></span>
        <span>
          <!-- <p>Sub Total: </p>
          <p>Tax: </p> -->
          <p>Grand Total: </p>
        </span>
        <span>
          <!-- <p>{{ printData.allPrice * 0.87 | getArea }} </p>
          <p>{{ printData.allPrice * 0.13 | getArea }} </p> -->
          <p>{{ printData.allPrice | getArea }}</p>
        </span>
      </div>
    </div>
    <div class="footer"  v-if="printData.type != 'delivery'">
      <h4 class="title">Terms and Conditions</h4>
      <p style="white-space: pre-wrap;">{{ printData.clause_detail }}</p>
    </div>
    <div class="signature"  v-if="printData.type == 'delivery'">
      <div class="box">
        <p>Signature<span></span></p>
        <p>Date<span></span></p>
      </div>
      <p class="remark" style="white-space: pre-wrap;">{{ printData.remark }}</p>
    </div>
  </div>
</template>

<script>

export default {
  data() {
    return {
      article: '',
      fullscreenLoading: true,
      printData: {}
    }
  },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  created() {

    let print = sessionStorage.getItem("print");
    print = JSON.parse(print);
    let uuidArr = print.uuid.split('-')
    switch (uuidArr[0]) {
      case 'Q':
        print.type = 'quotation'
        break;
      case 'I':
        print.type = 'invoice'
        break;
      case 'D':
        print.type = 'delivery'
        break;

      default:
        break;
    }
    console.log(print);//注意JSON转译
    this.printData = print
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      // import('../pdf/content.js').then(data => {
      // const { title } = data.default
      document.title = this.printData.uuid
      // this.article = `<h1>test</h1>`
      setTimeout(() => {
        this.fullscreenLoading = false
        this.$nextTick(() => {
          window.print()
        })
      }, 3000)
      // })
    }
  }
}
</script>

<style lang="scss">
@mixin clearfix {
  &:before {
    display: table;
    content: '';
    clear: both;
  }

  &:after {
    display: table;
    content: '';
    clear: both;
  }
}

.main-article {
  padding: 20px;
  margin: 0 auto;
  display: block;
  width: 740px;
  background: #fff;
}

.article__heading {
  position: relative;
  padding: 0 0 20px;
  overflow: hidden;
}

.article__heading__title {
  text-align: center;
  display: block;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  word-wrap: break-word;
  overflow-wrap: break-word;
  font-size: 32px;
  line-height: 48px;
  font-weight: 600;
  color: #333;
  overflow: hidden;

  >p {
    margin: 20px auto;
  }
}

.formdata {
  margin-top: 40px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;

  .box {}

  .left {
    display: inline-block;
    width: 550px;
  }

  .right {
    display: inline-block;
    width: 550px;
    margin-left: 124px;

    .col {
      >span {
        width: 250px;
      }
    }

    .name {

      width: 140px !important;
    }
  }


  .col {
    margin-bottom: 8px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;

    >span {
      display: inline-block;
      width: 460px;

      &.name {
        font-weight: bold;
        width: 90px;
      }
    }



    // .right{
    //   .name{
    //     width: 90px;
    //   }

    // }
  }
}

// 标题
.title-box {
  display: flex;
  flex-direction: row;
  align-items: center;

  >img {
    display: inline-flex;
    width: 63.65px;
    height: 63.65px;
  }

  .title {
    margin-left: 20px;
    width: 500px;

    >h2 {
      margin: 0 0 4px 0;
    }

    >p {

      margin: 2px 0;
    }
  }

  >h1 {
    margin-left: 90px;
  }
}

.table {
  width: 1060px;
  border: 1px solid #000;

  .title {

    font-weight: bold;
    border-bottom: 1px solid #000;
  }

  .title,
  .content {
    display: flex;
    flex-direction: row;

    span {
      display: inline-block;
      line-height: 48px;
      padding: 0 8px;
      border-right: 1px solid #000;

      &.desc {
        padding: 8px;

        h4 {
          margin: 4px 0 0 0;
          line-height: 1;
        }

        p {
          margin: 0;
          margin-top: 4px;
          line-height: 1.1;
        }
      }
    }


    span:nth-child(1) {
      width: 120px;
    }

    span:nth-child(2) {
      width: 600px;

      &.delivery {
        width: 820px;
      }
    }

    span:nth-child(3) {
      width: 80px;
    }

    span:nth-child(4) {
      width: 120px;
    }

    span:nth-child(5) {
      width: 100px;
    }

    :last-child {
      border-right: none
    }
  }

  .total {
    display: flex;
    flex-direction: row;
    border-top: 1px solid #000;

    span {
      display: inline-block;
      padding: 20px 0;
      display: inline-block;
      padding: 0 8px;
      border-right: 1px solid #000;
      font-weight: bold;

      p {
        margin: 2px;
      }
    }

    span:nth-child(1) {
      width: 120px;
    }

    span:nth-child(2) {
      width: 800px;
      text-align: right;
      padding: 6px 8px 50px 8px;
    }

    span:nth-child(3) {
      width: 100px;
      font-weight: normal;
      padding: 6px 8px 50px 8px;
    }

    :last-child {
      border-right: none
    }
  }
}

.block {
  display: inline-block;
  height: 80px;
}

.footer {
  margin-top: 15px;
  position: relative;
  border: 1px solid #000;
  padding: 8px;
  width: 1060px;

  .title {
    margin: 0;
  }

  p {
    margin: 0;
    margin-top: 6px;
    line-height: 1.1;
  }
}

.signature {
  position: absolute;
  top: 1400px;

  >.box {

    >p {
      position: relative;
      display: inline-block;
      font-weight: bold;
      border-top: 1px solid #000;
      width: 250px;
      padding-top: 12px;

      &:nth-child(2) {
        margin-left: 500px;
      }

      // &::before {
      //   content: "";
      //   display: block;
      //   position: absolute;
      //   top: -10px;
      //   left: 0;
      //   width: 250px;
      //   height: 2px;
      //   background-color: #000 !important;
      // }
    }
  }

  >.remark {

    position: relative;
    border: 1px solid #000;
    padding: 8px;
    width: 1060px;
  }
}
</style>
