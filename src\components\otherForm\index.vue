<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="160px">
      <div class="box" style="width:360px">

        <!-- <h3>{{ typeName =='發票' }}的基礎信息</h3> -->
        <h3>{{ typeName == '發票' ? $t('quotation.bill.name') : $t('quotation.delivery.name')
}}{{ $t('quotation.otherName1') }}</h3>
        <el-form-item :label="$t('quotation.project')" prop="name">
          <el-input v-model="form.name" :disabled="true" />
        </el-form-item>

        <el-form-item :label="$t('quotation.quoteDate')" prop="date">
          <el-date-picker v-model="form.date" type="date" :disabled="true" placeholder="Please select"
            style="width:200px">
          </el-date-picker>
        </el-form-item>


        <el-form-item :label="$t('quotation.company')" prop="company">
          <el-select v-model="form.company" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :remote-method="remoteMethod_companyOptions" :loading="loading"
            default-first-option @change="changeCompany" :disabled="true">
            <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.companyAddress')">
          <el-input v-model="form.address" :disabled="true" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>

        <el-form-item :label="$t('quotation.currency')">
          <el-input v-model="form.currency" :disabled="true" />
        </el-form-item>

        <el-form-item :label="$t('quotation.contact')" prop="company">
          <el-select v-model="form.contact_person" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :remote-method="remoteMethod_contactPersonOptions" :loading="loading"
            default-first-option :disabled="true">
            <el-option v-for="item in contactPersonOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>



        <el-form-item :label="$t('quotation.payment')" prop="company">
          <el-select v-model="form.payment_name" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :loading="loading" default-first-option @change="changePayment"
            :disabled="true">
            <el-option v-for="item in paymentOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.paymentRemark')">
          <el-input v-model="form.payment_detail" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" :disabled="true" />
        </el-form-item>
        <div v-if="this.typeName != '發票'">
          <el-form-item :label="$t('quotation.delivery.recipient')" prop="recipient">
            <el-input v-model="form.recipient" placeholder="Please input" />
          </el-form-item>


          <el-form-item :label="$t('quotation.delivery.recipientTel')" prop="recipientTel">
            <el-input v-model="form.recipientTel" placeholder="Please input" />
          </el-form-item>


          <el-form-item :label="$t('quotation.delivery.recipientAddress')" prop="recipientAddress">
            <el-input v-model="form.recipientAddress" placeholder="Please input" />
          </el-form-item>
        </div>
      </div>



      <div class="box" v-if="typeName == '發票'">
        <!-- <h3>{{ typeName }}的產品內容</h3> -->
        <h3>{{ typeName == '發票' ? $t('quotation.bill.name') : $t('quotation.delivery.name')
}}{{ $t('quotation.otherName2') }}</h3>

        <el-table v-loading="listLoading" show-summary :data="form.content" :summary-method="getSummaries" border fit
          highlight-current-row style="width: 1400px">
          <el-table-column align="center" :label="$t('quotation.index')" min-width="36px">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>


          <el-table-column align="left" min-width="200px" :label="$t('quotation.product')">
            <template slot-scope="scope">
              <template>
                <h3 style="font-weight:bold">{{ scope.row.label }}</h3>
                <p class="describe">{{ scope.row.describe }}</p>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" min-width="50px" :label="$t('quotation.ProductCategory')">
            <template slot-scope="{row}">
              <template>
                <span>{{ row.product_category }}</span>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="num" min-width="50px" :label="$t('quotation.number')">
            <template slot-scope="{row}">
              <template>
                <p class="describe">{{ row.num }}</p>
              </template>
            </template>
          </el-table-column>


          <el-table-column align="center" prop="price" min-width="70px" :label="$t('quotation.unitPrice')"
            v-if="this.typeName == '發票'">
            <template slot-scope="{row}">
              <template>
                <p class="describe">{{ row.price | getArea }}</p>

              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="totalPrice" min-width="80px" :label="$t('quotation.allPrice')"
            v-if="this.typeName == '發票'">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.totalPrice = scope.row.num && scope.row.price ? scope.row.num * scope.row.price :
    '' | getArea
}}</span>
              </template>
            </template>
          </el-table-column>


          <el-table-column align="center" min-width="80px" :label="$t('quotation.cost')" v-if="this.typeName == '發票'">
            <template slot-scope="{row}">
              <template>
                <span>{{ row.cost * row.num ? row.cost * row.num : 0 | getArea }}</span>
              </template>
            </template>
          </el-table-column>


        </el-table>
      </div>


      <div class="box" v-if="typeName != '發票'">
        <!-- <h3>{{ typeName }}的產品內容</h3> -->
        <h3>{{ typeName == '發票' ? $t('quotation.bill.name') : $t('quotation.delivery.name')
}}{{ $t('quotation.otherName2') }}</h3>

        <el-table v-loading="listLoading" show-summary :data="form.content" :summary-method="getSummaries" border fit
          highlight-current-row style="width: 1400px">
          <el-table-column align="center" :label="$t('quotation.index')" min-width="30px">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>


          <el-table-column align="left" min-width="200px" :label="$t('quotation.product')">
            <template slot-scope="scope">
              <template>
                <el-select v-model="form.content[scope.$index].label" clearable filterable remote reserve-keyword
                  class="filter-item" placeholder="Please select" :remote-method="remoteMethod_product"
                  @change="changeContent(scope.row, scope.$index)" :loading="loading" default-first-option>
                  <el-option v-for="(item, i) in productOptions" :key="item.value" :label="item.label" :value="item" />
                </el-select>
                <el-input class="describe" v-model="scope.row.describe" :autosize="{ minRows: 2, maxRows: 4 }"
                  type="textarea" />
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" min-width="40px" :label="$t('quotation.ProductCategory')">
            <template slot-scope="{row}">
              <template>
                <span>{{ row.product_category }}</span>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="num" min-width="40px" :label="$t('quotation.number')">
            <template slot-scope="{row}">
              <template>
                <el-input-number v-model="row.num" :min="1" :max="999" size="small"
                  label="please input"></el-input-number>
              </template>
            </template>
          </el-table-column>


          <el-table-column align="center" prop="price" min-width="70px" :label="$t('quotation.unitPrice')"
            v-if="this.typeName == '發票'">
            <template slot-scope="{row}">
              <template>
                <el-input v-model="row.price" size="small" />
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="totalPrice" min-width="80px" :label="$t('quotation.allPrice')"
            v-if="this.typeName == '發票'">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.totalPrice = scope.row.num && scope.row.price ? scope.row.num * scope.row.price :
    '' | getArea
}}</span>
              </template>
            </template>
          </el-table-column>


          <el-table-column align="center" min-width="80px" :label="$t('quotation.cost')" v-if="this.typeName == '發票'">
            <template slot-scope="{row}">
              <template>
                <span>{{ row.cost * row.num ? row.cost * row.num : 0 | getArea }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" :label="$t('quotation.delete')" width="120">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem(scope.$index)" />
            </template>
          </el-table-column>


        </el-table>

        <el-button type="primary" size="small" icon="el-icon-plus" plain class="add" @click="add">
          {{ $t('quotation.addRow') }}
        </el-button>
      </div>



      <div class="box">
        <h3>{{ $t('quotation.clauseTitle') }}</h3>
        <el-form-item :label="$t('quotation.clauseCategory')" prop="clause_type">
          <el-select v-model="form.clause" class="filter-item" placeholder="Please select" @change="changeClause">
            <el-option v-for="item in clauseOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('quotation.clauseDetail')">
          <el-input v-model="form.clause_detail" :autosize="{ minRows: 4, maxRows: 20 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>

        <el-form-item label="remark" v-if="typeName != '發票'">
          <el-input v-model="form.remark" :autosize="{ minRows: 4, maxRows: 20 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
      </div>

    </el-form>
    <!-- 送貨單 -->
    <div slot="footer" class="dialog-footer" v-if="typeName != '發票'">
      <el-button v-if="!this.formId" type="success" size="medium" @click="createData()">
        {{ $t('quotation.delivery.add') }}
      </el-button>
      <el-button v-if="this.formId" type="primary" size="medium" @click="updateData()">
        {{ $t('quotation.delivery.edit') }}
      </el-button>
      <el-button class="download" plain @click="downloadPdf" type="primary">
        {{ $t('quotation.downloadPdf') }}
      </el-button>
    </div>
    <!-- 發票 -->
    <div slot="footer" class="dialog-footer" v-if="typeName === '發票'">
      <el-button v-if="(!this.formId && form.bill == 0)" type="success" size="medium" @click="handleCreate(0)">
        {{ $t('quotation.bill.add') }}
      </el-button>
      <el-button v-if="(!this.formId && form.bill != 0)" type="success" size="medium" @click="handleCreate(1)">
        {{ $t('quotation.bill.edit') }}
      </el-button>
      <el-button class="download" plain @click="downloadPdf" type="primary">
        {{ $t('quotation.downloadPdf') }}
      </el-button>
    </div>

  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn, fillZero } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  props: ["formId", "typeName", "childEvent", "quotationId"],
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      baseUrl: "",
      // baseUrl: this.dialogState == '發票' ? '/content-manager/collection-types/api::bill.bill/' : '/content-manager/collection-types/api::delivery.delivery/',
      baseUrl1: '/content-manager/collection-types/api::bill.bill/',
      baseUrl2: '/content-manager/collection-types/api::delivery.delivery/',
      baseUrl3: '/content-manager/collection-types/api::quotation.quotation/',
      statisticalUrl: '/content-manager/collection-types/api::statistical.statistical/',
      companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      contactPersonUrl: '/content-manager/collection-types/api::contact-person.contact-person?filters[$and][0][state][$eq]=true',
      paymentUrl: '/content-manager/collection-types/api::payment-method.payment-method?filters[$and][0][state][$eq]=true',
      productUrl: '/content-manager/collection-types/api::product.product?filters[$and][0][state][$eq]=true',
      clausetUrl: '/content-manager/collection-types/api::clause.clause?filters[$and][0][state][$eq]=true',
      list: [],
      totle: 0,
      listLoading: false,
      loading: false,
      text: "- Please do not hesitat to contact us (852) 82000427, if you require any information.\n- Notice that this quotation is availabl within four weeks.\n- Unless declare about installation, parts and design work, otherwise which are not included. - Cheque(s) please made payable to 'eShowcase limited ",
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: '啟用'
      }, {
        value: '0',
        label: '關閉'
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      // 公司
      companyOptions: [],
      // 聯繫人
      contactPersonOptions: [],
      // 付款方式
      paymentOptions: [],
      // 產品
      productOptions: [],
      // 條文
      clauseOptions: [],
      form: {},
      formRow: {},
      uuid: '',
      rebBill: false,
      // 列表請求參數
      listQuery: {
      },
      baseContent: {
        "id": "",
        "productCategory": "",
        "num": "",
        "price": "",
        "totalPrice": "",
        "cost": ""
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        recipient: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        recipientTel: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        recipientAddress: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 部门
        department: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 职位
        position: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 狀態
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ]
      }
    }
  },
  created() {

    this.baseUrl = this.typeName == '發票' ? this.baseUrl1 : this.baseUrl2
    // 如果有數據才拉取，否則不拉取
    if (this.formId) {
      this.listQuery = {
        'sort': 'id:ASC',
        'filters[$and][0][id][$eq]': this.formId
      }
      this.getList(this.listQuery)
    } else {
      this.baseUrl = this.baseUrl3
      this.listQuery = {
        'sort': 'id:ASC',
        'filters[$and][0][id][$eq]': this.quotationId
      }
      this.getList(this.listQuery)
      this.baseUrl = this.typeName == '發票' ? this.baseUrl1 : this.baseUrl2

    }
    // 獲取公司
    this.remoteMethod_companyOptions()
    // 獲取聯繫人
    this.remoteMethod_contactPersonOptions()
    // 獲取付款方式
    this.remoteMethod_paymentOptions()
    //设置默认今天
    this.$set(this.form, "date", this.dateFormat("YYYY-mm-dd", new Date()))
    // 獲取條文內容
    this.getRelation(this.clausetUrl).then((result) => {
      const res = result
      const arrList = []
      // 把返回数据转换为table需要的格式
      for (let index = 0; index < res.length; index++) {
        const element = res[index]
        const arr = {
          value: res[index].id,
          label: res[index].name,
          language: res[index].language,
          detail: res[index].detail
        }
        arrList.push(arr)
      }
      this.clauseOptions = arrList
    })
  },
  mounted() {

  },
  methods: {

    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true

      getData(this.baseUrl, params).then(response => {
        const res = response.results[0]
        console.log(res);
        const arr = {
          // 公司名称
          company: res.company ? res.company.name : '',
          company_value: res.company ? res.company.id : '',
          // 地址
          address: res.company ? res.company.address : '',
          // 聯繫人
          contact_person: res.contact_person.name,
          contact_person_value: res.contact_person.id,
          // 付款方式
          payment_name: res.payment_name,
          payment_detail: res.payment_detail,
          // 條文詳情
          clause: res.clause_name,
          clause_detail: res.clause_detail,
          // 貨幣
          currency: res.currency.name ? res.currency.name : res.currency,
          // 項目名稱
          name: res.name,
          // 報價日期
          date: res.date,
          // 報價單號
          id: res.id,
          // uuid
          uuid: res.uuid,
          // 總價
          allPrice: res.allPrice,
          // 總成本
          allCost: res.allCost,
          // 發票
          bill: res.bill ? res.bill : 0,
          // 送貨單
          delivery: res.deliveries ? res.deliveries.count : 0,
          // 狀態

          recipient: res.recipient ? res.recipient : (res.contact_person ? res.contact_person.name : ''),
          recipientTel: res.recipientTel ? res.recipientTel : (res.contact_person.phone ? res.contact_person.phone[0] : ''),
          recipientAddress: res.recipientAddress ? res.recipientAddress : (res.company ? res.company.address : ''),

          // 產品詳情
          content: res.content,

          // 狀態
          state: res.state,
          stateType: res.state ? '啟用' : '關閉',
          // 修改時間
          updatedAt: timeChange(res.updatedAt),
          // 修改人
          updatedBy: res.updatedBy.firstname

        }
        this.form = arr
        this.formRow = arr
        if (this.typeName != '發票' && !res.remark) {
          let remark = "- Notice must be given to us of any goods not received within 10 days taken from the date of dispatch stated on the invoice.\n- Any shortage or damage must be notified within 72 hours of receipt of goods.\n- Complaints can only be accepted if made in writing within 30 days of receipt of goods.\n- No goods may be returned without prior authorisation from the company"

          this.$set(this.form, "remark", remark)
          this.$set(this.formRow, "remark", remark)
        } else {

          this.$set(this.form, "remark", res.remark)
          this.$set(this.formRow, "remark", res.remark)
        }

        this.listLoading = false
      })
    },
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        };
      };
      return fmt;
    },

    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字
    async getRelation(url, p) {
      let params = ''
      let rt
      if (p) {
        params = {
          'filters[$and][1][name][$contains]': p
        }
      }
      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        rt = res
        this.listLoading = false
      })
      return rt
    },
    // 確認修改的事件
    updateData() {
      // 清除空的content
      this.clearContent();
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          const temp = {
            id: this.form.id,
            name: this.form.name ? this.form.name : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            company: this.form.company.value ? relationReturn(this.form.company.value, this.formRow.company_value) : relationReturn(this.form.company_value, this.formRow.company_value),
            contact_person: this.form.contact_person.value ? relationReturn(this.form.contact_person.value, this.formRow.contact_person_value) : relationReturn(this.form.contact_person_value, this.formRow.contact_person_value),
            // bill: relationCreateReturn(this.form.bill.value),
            // 數組格式需判斷是否有內容，沒有則用[""]標識
            content: this.form.content.length ? this.form.content : [''],


            payment_name: this.form.payment_name.label ? this.form.payment_name.label : this.form.payment_name,
            payment_detail: this.form.payment_detail ? this.form.payment_detail : '',
            clause_name: this.form.clause.label ? this.form.clause.label : this.form.clause,
            clause_detail: this.form.clause_detail ? this.form.clause_detail : '',
            allPrice: this.form.allPrice ? this.form.allPrice : '',
            date: this.form.date.length == 10 ? this.form.date : this.dateFormat("YYYY-mm-dd", this.form.date),
            // frequency: 1,
            currency: this.form.currency.label,
            // 不可修改对应的报价单，所以不允许修改
            quotation: {
              "disconnect": [],
              "connect": []
            }

          }

          if (this.typeName != '發票') {
            // 送貨單號
            temp.recipient = this.form.recipient
            temp.recipientTel = this.form.recipientTel
            temp.recipientAddress = this.form.recipientAddress
            temp.remark = this.form.remark
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'put', temp.id, tempData).then(() => {
            // 關閉彈框
            this.childEvent(false)
            this.$notify({
              title: 'Success',
              message: 'Edit Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 發票新增的判斷
    async handleCreate(n) {
      // 0為開具發票，1為重開發票
      console.log(n);
      if (n) {
        this.listQuery = {
          page: 1,
          pageSize: 1,
          sort: 'id:DESC',
          'filters[$and][0][quotation][id][$eq]': this.form.id
        }
        await getData(this.baseUrl1, this.listQuery).then(response => {
          const res = response.results
          const arrList = []
          // 如果沒有單據，則從01開始新增
          if (res[0]) {
            // 把返回数据转换为table需要的格式
            for (let index = 0; index < res.length; index++) {
              const arr = {
                // id
                id: res[index].id,
                uuid: res[index].uuid,

              }
              arrList.push(arr)
            }
            let list = arrList[0]
            this.form.id = list.id
            // 設置為重開發票的狀態
            this.rebBill = true
            this.updateData()
          }
        })
      } else {
        this.createData()
      }
    },
    // 提交創建的事件
    createData() {
      // 清除空的content
      this.clearContent();
      this.$refs['form'].validate(async (valid) => {
        console.log(this.form)
        if (valid) {

          // 取UUid
          if (this.typeName === '發票') {
            await this.getUid("I")

          } else {
            await this.getUid("D")
          }
          let companyId = this.form.company.value ? this.form.company.value : this.form.company_value
          const temp = {
            name: this.form.name ? this.form.name : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            company: relationCreateReturn(companyId),
            contact_person: this.form.contact_person.value ? relationCreateReturn(this.form.contact_person.value) : relationCreateReturn(this.form.contact_person_value),
            // currency: this.form.currency.value ? relationCreateReturn(this.form.currency.value) : relationCreateReturn(this.form.currency_value),
            // bill: relationCreateReturn(this.form.bill.value),
            // 數組格式需判斷是否有內容，沒有則用[""]標識
            content: this.form.content.length ? this.form.content : [''],

            payment_name: this.form.payment_name.label ? this.form.payment_name.label : this.form.payment_name,
            payment_detail: this.form.payment_detail ? this.form.payment_detail : '',
            clause_name: this.form.clause.label ? this.form.clause.label : this.form.clause,
            clause_detail: this.form.clause_detail ? this.form.clause_detail : '',
            allPrice: this.form.allPrice ? this.form.allPrice : '',
            date: this.form.date ? this.form.date : '',
            // frequency: 1,
            currency: this.form.currency,
            uuid: this.uuid,
            // uid: '10001',
            // 不可修改对应的报价单，所以不允许修改
            quotation: {
              connect: [{
                id: this.quotationId
              }],
              disconnect: []
            }

          }

          if (this.typeName != '發票') {
            // 送貨單號
            temp.recipient = this.form.recipient
            temp.recipientTel = this.form.recipientTel
            temp.recipientAddress = this.form.recipientAddress
            temp.remark = this.form.remark
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          console.log("create")
          postData(this.baseUrl, 'post', '', tempData).then(response => {
            if (this.typeName == '發票') {

              console.log(response);
              // 創建成功後同步數據新增統計表
              const statisticaTemp = {
                // id
                quotation: relationCreateReturn(this.quotationId),
                company: relationCreateReturn(companyId),
                bill: relationCreateReturn(response.id),
                finalCost: this.form.allCost,
                profit: this.form.allPrice - this.form.allCost,
                allReceive: 0,
                content: []
              }
              const statisticaData = Object.assign({}, statisticaTemp)
              postData(this.statisticalUrl, 'post', '', statisticaData).then(response => {
                // 創建成功後同步數據新增統計表
                this.dialogFormVisible = false
                this.$notify({
                  title: 'Success',
                  message: 'Create Successfully',
                  type: 'success',
                  duration: 1000
                })
              })
            }
            this.childEvent(false)
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    changeCountry(item) {
      this.form.currency = item.currency
    },
    // 新增或者修改时，新增电话或者手机的方法
    add() {
      this.form.content.push(this.baseContent)
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(delete_index) {
      this.form.content.splice(delete_index, 1)
    },
    // 遠程搜索 公司
    remoteMethod_companyOptions(query) {
      if (query !== '') {
        // 獲取公司
        this.getRelation(this.companyUrl, query).then((result) => {
          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              country: res[index].country,
              address: res[index].address
            }
            arrList.push(arr)
          }
          this.companyOptions = arrList
        })
      } else {
        this.getRelation(this.companyUrl).then((result) => {

          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              address: res[index].address
            }
            arrList.push(arr)
          }
          this.companyOptions = arrList
        })
      }
    },
    // 遠程搜索 聯繫人
    remoteMethod_contactPersonOptions(query) {
      if (query !== '') {
        this.getRelation(this.contactPersonUrl, query).then((result) => {
          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
            }
            arrList.push(arr)
          }
          this.contactPersonOptions = arrList
        })
      } else {
        this.getRelation(this.contactPersonUrl).then((result) => {
          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
            }
            arrList.push(arr)
          }
          this.contactPersonOptions = arrList
        })
      }

    },

    // 遠程搜索 付款方式
    remoteMethod_paymentOptions() {
      this.getRelation(this.paymentUrl).then((result) => {
        const res = result
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            remark: res[index].remark
          }
          arrList.push(arr)
        }
        this.paymentOptions = arrList
      })
    },
    // 遠程搜索 產品
    remoteMethod_product(query) {
      if (query !== '') {
        this.getRelation(this.productUrl, query).then((result) => {
          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              num: 1,
              price: res[index].price,
              cost: res[index].cost,
              describe: res[index].describe,
              // 產品分類
              product_category: res[index].product_category.name,
              product_category_id: res[index].product_category.id,
            }
            arrList.push(arr)
          }
          this.productOptions = arrList
        })
      } else {
        this.getRelation(this.companyUrl).then((result) => {

          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              price: res[index].price,
              cost: res[index].cost,
              describe: res[index].describe,
              // 產品分類
              product_category: res[index].product_category.name,
              product_category_id: res[index].product_category.id,
            }
            arrList.push(arr)
          }
          this.productOptions = arrList
        })
      }

      console.log(this.productOptions)
    },
    changeCompany(item) {
      this.$set(this.form, "address", item.address)
      this.form.currency = item.country.currency
    },

    changePayment(item) {
      this.$set(this.form, "payment_detail", item.remark)
    },

    changeClause(item) {
      this.$set(this.form, "clause_detail", item.detail)
    },
    changeContent(item, index) {
      console.log('item', item.label);
      console.log('i', index);
      // this.$set(this.form.content, [index], item)
      this.$set(this.form.content, index,
        {
          "value": item.label.value,
          "label": item.label.label,
          "num": item.label.num,
          "price": item.label.price,
          "cost": item.label.cost,
          "describe": item.label.describe,
          "product_category": item.label.product_category,
          "product_category_id": item.label.product_category_id
        })
      console.log(this.form.content[index]);
    },

    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = '總數';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (index === 5) {
            this.form.allPrice = sums[index];
            sums[index] = this.$options.filters['getArea'](sums[index]);
          }
          else if (index === 4) {
            sums[index] = '總價';
          }

        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    clearContent() {
      for (let index = 0; index < this.form.content.length; index++) {
        const element = this.form.content[index];
        // 如果产品名称为空，则删除这个数据
        if (!element.label) {
          this.deleteItem(index);
        }
      }
    },

    // 獲取id來組成UUid
    async getUid(a) {
      let address = a
      this.listQuery = {
        page: 1,
        pageSize: 1,
        sort: 'id:DESC',
      }
      await getData(this.baseUrl, this.listQuery).then(response => {
        const res = response.results
        const arrList = []
        // 如果沒有單據，則從01開始新增
        if (res[0]) {
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const arr = {
              // id
              id: res[index].id,
              uuid: res[index].uuid,

            }
            arrList.push(arr)
          }
          let list = arrList[0]
          let numbers = list.uuid.match(/\d+/g);
          let num = parseInt(numbers[0])
          this.uuid = address + '-' + fillZero(num + 1, 5)
          console.log(this.uuid);
        } else {
          // 第一個uuid
          this.uuid = address + '-' + '00128'
        }
      })
    },
    // 导出pdf
    downloadPdf() {
      let formRow = JSON.stringify(this.formRow);
      console.log(formRow);
      let router = this.$router.resolve({ path: '/pdf/download', query: formRow })
      sessionStorage.setItem("print", formRow);
      window.open(router.href, '_blank')
    },
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: center;
}

.describe {
  margin-top: 10px;
}

.download {
  position: absolute;
  right: 40px;
}
</style>
