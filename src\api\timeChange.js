// 时间格式转换
export function timeChange(date) {
  const dateString = new Date(date)
  // 注意js里面的getMonth是从0开始的
  const FormattedDateTime =
    dateString.getFullYear() + '-' +
    returnFloat((dateString.getMonth() + 1), 'left') + '-' +
    returnFloat(dateString.getDate(), 'left') + ' ' +
    returnFloat(dateString.getHours(), 'left') + ':' +
    returnFloat(dateString.getMinutes(), 'left')
  return FormattedDateTime
}
// 时间补零
export function returnFloat(value, type) {
  var s = value.toString()
  if (s.length == 1) {
    if (type == 'left') {
      value = '0' + value.toString()
    } else {
      value = value.toString() + '0'
    }
    return value
  } else {
    return value
  }
}
