<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="160px">
      <div class="box" style="width:360px">
        <h3>Quotation Id: <span style="color:red;">{{ form.uuid }}</span></h3>
        <el-form-item :label="$t('quotation.project')" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item :label="$t('quotation.quoteDate')" prop="date">
          <el-date-picker v-model="form.date" type="date" placeholder="Please select" style="width:200px">
          </el-date-picker>
        </el-form-item>


        <el-form-item :label="$t('quotation.company')" prop="company">
          <el-select v-model="form.company" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :remote-method="remoteMethod_companyOptions" :loading="loading"
            default-first-option @change="changeCompany">
            <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.companyAddress')">
          <el-input v-model="form.address" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>

        <el-form-item :label="$t('quotation.currency')">
          <el-select v-model="form.currency" clearable filterable reserve-keyword class="filter-item"
            placeholder="Please select" :loading="loading" default-first-option>
            <el-option v-for="item in currencyOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.contact')" prop="company">
          <el-select v-model="form.contact_person" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :remote-method="remoteMethod_contactPersonOptions" :loading="loading"
            default-first-option>
            <el-option v-for="item in contactPersonOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>



        <el-form-item :label="$t('quotation.payment')" prop="company">
          <el-select v-model="form.payment_name" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :loading="loading" default-first-option @change="changePayment">
            <el-option v-for="item in paymentOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.paymentRemark')">
          <el-input v-model="form.payment_detail" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
      </div>


      <div class="box">
        <h3>{{ $t('quotation.productTitle') }}</h3>
        <el-table v-loading="listLoading" show-summary :summary-method="getSummaries" :data="form.content" border fit
          highlight-current-row style="width: 1400px" :cell-style="cellStyle">
          <el-table-column align="center" :label="$t('quotation.index')" min-width="30px">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>


          <el-table-column align="left" min-width="200px" :label="$t('quotation.product')">
            <template slot-scope="scope">
              <template>
                <el-select v-model="form.content[scope.$index].label" clearable filterable remote reserve-keyword
                  class="filter-item" placeholder="Please select" :remote-method="remoteMethod_product"
                  @change="changeContent(scope.row, scope.$index)" :loading="loading" default-first-option>
                  <el-option v-for="(item, i) in productOptions" :key="item.value" :label="item.label" :value="item" />
                </el-select>

                <el-input class="describe" v-model="scope.row.describe" :autosize="{ minRows: 2, maxRows: 4 }"
                  type="textarea" />
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" min-width="75px" :label="$t('quotation.ProductCategory')">
            <template slot-scope="{row}">
              <template>
                <span>{{ row.product_category }}</span>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="num" min-width="100px" :label="$t('quotation.number')">
            <template slot-scope="{row}">
              <template>
                <el-input-number v-model="row.num" :min="1" :max="999" size="small"
                  label="please input"></el-input-number>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="price" min-width="70px" :label="$t('quotation.unitPrice')">
            <template slot-scope="{row}">
              <template>
                <el-input v-model.trim="row.price" @focus="uninputMoney($event)" @blur="inputMoney($event)"
                  size="small" />
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="totalPrice" min-width="80px" :label="$t('quotation.allPrice')">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.totalPrice = scope.row.num && scope.row.price ? scope.row.num * scope.row.price :
                  '' | getArea
                }}</span>
              </template>
            </template>
          </el-table-column>


          <el-table-column align="center" prop="totalCost" min-width="80px" :label="$t('quotation.cost')">
            <template slot-scope="scope">
              <template>
                <span class="cost">{{ scope.row.totalCost = scope.row.num && scope.row.cost ? scope.row.num *
                  scope.row.cost :
                  0 | getArea
                }}</span>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" label="移除" width="120" :label="$t('quotation.delete')">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem(scope.$index)" />
            </template>
          </el-table-column>


        </el-table>

        <el-button type="primary" size="small" icon="el-icon-plus" plain class="add" @click="add">
          {{ $t('quotation.addRow') }}
        </el-button>
      </div>



      <div class="box">
        <h3>{{ $t('quotation.clauseTitle') }}</h3>
        <el-form-item :label="$t('quotation.clauseCategory')" prop="clause_type">
          <el-select v-model="form.clause" class="filter-item" placeholder="Please select" @change="changeClause">
            <el-option v-for="item in clauseOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('quotation.clauseDetail')">
          <el-input v-model="form.clause_detail" :autosize="{ minRows: 4, maxRows: 20 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
      </div>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="success" size="medium" @click="dialogConfirm('create')">
        {{ $t('quotation.add') }}
      </el-button>
      <el-button type="primary" size="medium" plain @click="dialogConfirm('add')">
        {{ $t('quotation.save') }} +1
      </el-button>
      <el-button type="primary" size="medium" @click="dialogConfirm('updata')">
        {{ $t('quotation.save') }}
      </el-button>
    </div>
    <!-- 發票與報價單 -->
    <div class="box other">

      <h3>{{ $t('quotation.billTitle') }}</h3>
      <div>
        <el-button type="primary" size="medium" @click="handleCreate('bill')">

          {{ form.bill == 0 ? $t('quotation.bill.add') : $t('quotation.bill.edit') }}
        </el-button>
        <el-button type="primary" size="medium" @click="handleCreate('delivery')">
          {{ $t('quotation.delivery.add') }}
        </el-button>
      </div>
      <div class="otherTable">
        <div class="title">
          <span>{{ $t('quotation.bill.name') }}</span>
          <span>{{ $t('quotation.delivery.name') }}</span>
        </div>
        <div class="number">
          <div>
            <el-button @click="handleClickBill('bill', form.id)" type="text" v-if="form.bill">
              {{ form.bill.uuid }}
            </el-button>
            <span v-if="!form.bill">{{ $t('quotation.bill.text') }}</span>
          </div>
          <div>
            <el-button @click="handleClickBill('delivery', form.id)" type="text" v-if="form.delivery > 0">{{
              form.delivery
            }}
            </el-button>
            <span v-if="form.delivery == 0">{{ form.delivery }}</span>
          </div>
        </div>
      </div>
      <el-button class="download" plain @click="downloadPdf" type="primary">
        {{ $t('quotation.downloadPdf') }}
      </el-button>
    </div>


    <el-dialog
      :title="tableTextMap[dialogTableStatus] == '發票' ? $t('quotation.bill.name') : $t('quotation.delivery.name')"
      :visible.sync="dialogTableVisible" width="90%" :before-close="handleDialogClose">
      <otherTable :tableId="tableId" :dialogState="dialogTableStatus" :key="time" />
    </el-dialog>

    <el-dialog
      :title="(tableTextMap[dialogTableStatus] == '發票' ? $t('quotation.bill.name') : $t('quotation.delivery.name')) + ' ' + $t('table.detail')"
      :visible.sync="dialogFormVisible" append-to-body width="90%">
      <otherForm :formId="formId" :quotationId="newQuotationId" :typeName="tableTextMap[dialogTableStatus]"
        :childEvent="getDialogVisble" :key="timer" />
    </el-dialog>
  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn, fillZero } from '@/api/tablefunction'
import otherTable from '@/components/otherTable'
import otherForm from '@/components/otherForm'

export default {
  name: 'Product',
  components: { otherTable, otherForm },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::quotation.quotation/',
      statisticalUrl: '/content-manager/collection-types/api::statistical.statistical/',
      companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      contactPersonUrl: '/content-manager/collection-types/api::contact-person.contact-person?filters[$and][0][state][$eq]=true',
      paymentUrl: '/content-manager/collection-types/api::payment-method.payment-method?filters[$and][0][state][$eq]=true',
      currencytUrl: '/content-manager/collection-types/api::currency.currency?filters[$and][0][state][$eq]=true',
      productUrl: '/content-manager/collection-types/api::product.product?filters[$and][0][state][$eq]=true',
      clausetUrl: '/content-manager/collection-types/api::clause.clause?filters[$and][0][state][$eq]=true',
      list: [],
      quotationId: '',
      // 傳值的id
      newQuotationId: '',
      totle: 0,
      listLoading: false,
      loading: false,
      // table弹框
      dialogTableVisible: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 父子table傳值
      tableId: '',
      // 弹框属于發票還是發貨單的列表
      dialogTableStatus: '',
      tableTextMap: {
        bill: '發票',
        delivery: '送貨單'
      },
      time: '',
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      // 公司
      companyOptions: [],
      // 貨幣
      currencyOptions: [],
      // 聯繫人
      contactPersonOptions: [],
      // 付款方式
      paymentOptions: [],
      // 產品
      productOptions: [],
      // 條文
      clauseOptions: [],
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      },
      // 公司的請求字段
      companyQarams: {},
      // 聯繫人的請求字段
      contactPersonQarams: {},
      baseContent: {
        "id": "",
        "productCategory": "",
        "num": "",
        "price": "",
        "totalPrice": "",
        "cost": ""
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 貨幣
        currency: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 职位
        position: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 狀態
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 獲取基礎數據
    this.quotationId = this.$route.query.id;
    this.listQuery = {
      'sort': 'id:ASC',
      'filters[$and][0][id][$eq]': this.quotationId
    }
    this.getList(this.listQuery)

    // 獲取公司
    this.remoteMethod_companyOptions()
    // 獲取付款方式
    this.remoteMethod_paymentOptions()
    // 獲取貨幣
    this.remoteMethod_currencyOptions()
    //设置默认今天
    this.$set(this.form, "date", this.dateFormat("YYYY-mm-dd", new Date()))
    // 獲取條文內容
    this.getRelation(this.clausetUrl).then((result) => {
      const res = result
      const arrList = []
      // 把返回数据转换为table需要的格式
      for (let index = 0; index < res.length; index++) {
        const element = res[index]
        const arr = {
          value: res[index].id,
          label: res[index].name,
          language: res[index].language,
          detail: res[index].detail
        }
        arrList.push(arr)
      }
      this.clauseOptions = arrList
    })
  },
  methods: {
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        };
      };
      return fmt;
    },
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            // 公司名称
            company: res[index].company ? res[index].company.name : '',
            company_value: res[index].company ? res[index].company.id : '',
            // 地址
            address: res[index].company ? res[index].company.address : '',
            // 聯繫人
            contact_person: res[index].contact_person ? res[index].contact_person.name : '',
            contact_person_value: res[index].contact_person ? res[index].contact_person.id : '',
            // 付款方式
            payment_name: res[index].payment_name,
            payment_detail: res[index].payment_detail,
            // 條文詳情
            clause: res[index].clause_name,
            clause_detail: res[index].clause_detail,
            // 貨幣
            currency: res[index].currency ? res[index].currency.name : "",
            currency_value: res[index].currency ? res[index].currency.id : "",
            // 項目名稱
            name: res[index].name,
            // 報價日期
            date: res[index].date,
            // 報價單號
            id: res[index].id,
            // 修改次數 R
            uuid: res[index].uuid,
            // 總價
            allPrice: res[index].allPrice,
            allCost: res[index].allCost,
            // 發票
            bill: res[index].bill ? res[index].bill : '',
            // 送貨單
            delivery: res[index].deliveries ? res[index].deliveries.count : 0,


            // 流水統計
            statistical: res[index].statistical,

            // 產品詳情
            content: res[index].content,

            // 狀態
            state: res[index].state,
            stateType: res[index].state ? this.$t('table.status1') : this.$t('table.status0'),
            // 修改時間
            updatedAt: timeChange(res[index].updatedAt),
            // 修改人
            updatedBy: res[index].updatedBy ? res[index].updatedBy.firstname : 'null'

          }
          arrList.push(arr)
        }
        let list = arrList[0]
        this.form = Object.assign({}, list) // copy obj
        this.formRow = Object.assign({}, list) // copy obj
        console.log(this.form);
        console.log(this.formRow);
        this.listLoading = false
      })
    },


    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字,第三個是限定條件
    async getRelation(url, p, target) {
      let params = ''
      let rt
      if (p && !target) {
        params = {
          'filters[$and][0][name][$contains]': p
        }
      } else if (p && target) {
        params = {
          'filters[$and][0][name][$contains]': p,
          'filters[$and][1][company][id][$eq]': target,
        }
      } else if (!p && target) {
        params = {
          'filters[$and][0][company][id][$eq]': target,
        }
      }

      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        rt = res
        this.listLoading = false
      })
      return rt
    },
    downloadPdf() {
      let formRow = JSON.stringify(this.formRow);
      let router = this.$router.resolve({ path: '/pdf/download', query: formRow })
      sessionStorage.setItem("print", formRow);
      window.open(router.href, '_blank')
    },
    // 確認修改的事件
    updateData() {
      // 清除空的content
      this.clearContent();
      this.$refs['form'].validate(async (valid) => {
        console.log(this.form)
        console.log(this.formRow)
        if (valid) {
          const temp = {
            id: this.form.id,
            name: this.form.name ? this.form.name : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            company: this.form.company.value ? relationReturn(this.form.company.value, this.formRow.company_value) : relationReturn(this.form.company_value, this.formRow.company_value),
            contact_person: this.form.contact_person.value ? relationReturn(this.form.contact_person.value, this.formRow.contact_person_value) : relationReturn(this.form.contact_person_value, this.formRow.contact_person_value),
            currency: this.form.currency.value ? relationReturn(this.form.currency.value, this.formRow.currency_value) : relationReturn(this.form.currency_value, this.formRow.currency_value),
            // bill: relationCreateReturn(this.form.bill.value),
            // 數組格式需判斷是否有內容，沒有則用[""]標識
            content: this.form.content.length ? this.form.content : [''],

            payment_name: this.form.payment_name.label ? this.form.payment_name.label : this.form.payment_name,
            payment_detail: this.form.payment_detail ? this.form.payment_detail : '',
            clause_name: this.form.clause.label ? this.form.clause.label : this.form.clause,
            clause_detail: this.form.clause_detail ? this.form.clause_detail : '',
            allPrice: this.form.allPrice ? this.form.allPrice : 0,
            allCost: this.form.allCost ? this.form.allCost : 0,
            date: this.form.date ? this.form.date : '',
            frequency: 1,
            // uid: '10001',
            state: true

          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'put', temp.id, tempData).then(() => {
            console.log(this.form.statistical)
            let newData = this.mathData(temp, this.form.statistical)
            const newTempData = Object.assign({}, newData)
            postData(this.statisticalUrl, 'put', this.form.statistical.id, newTempData).then(() => {

              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Edit Successfully',
                type: 'success',
                duration: 1000
              })
            })
          })
          this.$router.push({ path: '/quotation/quotation' })
        }
      })
    },

    mathData(quotationData, data) {
      let allOtherCost = 0 // 總添加成本
      let allReceive = 0 // 總收款金額

      let finalCost = 0 // 最終成本
      let profit = 0 // 總利潤
      let allPrice = quotationData.allPrice // 總價
      let allCost = quotationData.allCost // 總成本
      let content = data.content
      for (let index = 0; index < content.length; index++) {
        const element = content[index];
        let cost = element.cost ? parseFloat(element.cost) : 0
        let receive = element.receive ? parseFloat(element.receive) : 0
        allOtherCost += cost
        allReceive += receive
      }
      // 最終成本 = 總成本 + 其他添加成本
      finalCost = allCost + allOtherCost
      // 總利潤 = 總價 - 最終成本
      profit = allPrice - finalCost

      let newData = {
        finalCost: finalCost,
        profit: profit,
        allReceive: allReceive
      }
      return newData
    },
    // 提交創建的事件
    createData(type) {
      // 清除空的content
      this.clearContent();
      this.$refs['form'].validate(async (valid) => {
        console.log(this.form)
        console.log(valid);
        if (valid) {
          const temp = {
            name: this.form.name ? this.form.name : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            company: relationCreateReturn(this.form.company.value ? this.form.company.value : this.form.company_value),
            contact_person: relationCreateReturn(this.form.contact_person.value ? this.form.contact_person.value : this.form.contact_person_value),
            currency: relationCreateReturn(this.form.currency.value ? this.form.currency.value : this.form.currency_value),
            // bill: relationCreateReturn(this.form.bill.value),
            // 數組格式需判斷是否有內容，沒有則用[""]標識
            content: this.form.content.length ? this.form.content : [''],

            payment_name: this.form.payment_name.label ? this.form.payment_name.label : this.form.payment_name,
            payment_detail: this.form.payment_detail ? this.form.payment_detail : '',
            clause_name: this.form.clause.label ? this.form.clause.label : this.form.clause,
            clause_detail: this.form.clause_detail ? this.form.clause_detail : '',
            allPrice: this.form.allPrice ? this.form.allPrice : 0,
            allCost: this.form.allCost ? this.form.allCost : 0,
            date: this.form.date ? this.form.date : '',
            frequency: 1,

            // uid: '10001',
            state: true

          }
          // 根據按鈕不同的情況，分為創建和儲存+1的按鈕，如果是創建，則根據id的情況生成UUID，如果是儲存+1，則是根據uuid的尾數+1
          if (type === 'add') {
            await this.getUid(type)
            temp.uuid = this.uuid
          } else {
            console.log('temp.uuid start');
            await this.getUid()
            temp.uuid = this.uuid
            console.log('temp.uuid', temp.uuid);
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'post', '', tempData).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
          this.$router.push({ path: '/quotation/quotation' })
        }
      })
    },
    // 新增或者修改时，新增电话或者手机的方法
    add() {
      this.form.content.push(this.baseContent)
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(delete_index) {
      this.form.content.splice(delete_index, 1)
    },
    // 遠程搜索 公司
    remoteMethod_companyOptions(query) {

      if (query) {
        this.companyQarams['filters[$and][0][name][$contains]'] = query
        this.companyQarams['pageSize'] = 999

      } else {
        delete this.companyQarams['filters[$and][0][name][$contains]']
      }
      // 獲取公司
      getData(this.companyUrl, this.companyQarams).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            country: res[index].country,
            address: res[index].address
          }
          arrList.push(arr)
        }
        this.companyOptions = arrList
        this.listLoading = false
      })
      // this.getRelation(this.companyUrl, this.companyQarams).then((result) => {
      //   const res = result
      //   const arrList = []
      //   // 把返回数据转换为table需要的格式
      //   for (let index = 0; index < res.length; index++) {
      //     const element = res[index]
      //     const arr = {
      //       value: res[index].id,
      //       label: res[index].name,
      //       country: res[index].country,
      //       address: res[index].address
      //     }
      //     arrList.push(arr)
      //   }
      //   this.companyOptions = arrList
      // })

      console.log(this.companyOptions)
    },

    // 點擊發票數或送貨單數列表中的編輯中的事件
    handleClickBill(type, index) {
      this.listLoading = true
      this.time = new Date().getTime()
      this.dialogTableStatus = type
      this.tableId = index
      console.log(this.dialogTableStatus);
      this.dialogTableVisible = true
      this.listLoading = false
    },

    // 點擊新增發票或者送貨單的事件
    handleCreate(type) {
      this.dialogTableStatus = type
      this.newQuotationId = this.quotationId
      this.timer = new Date().getTime()
      this.dialogFormVisible = true
    },
    // 遠程搜索 聯繫人
    async remoteMethod_contactPersonOptions(query, target) {
      if (target) {
        this.contactPersonQarams['filters[$and][0][company][id][$eq]'] = target
        this.contactPersonQarams['pageSize'] = 999
      }
      if (query) {
        this.contactPersonQarams['filters[$and][0][name][$contains]'] = query
        this.contactPersonQarams['pageSize'] = 999

      } else {
        delete this.contactPersonQarams['filters[$and][0][name][$contains]']
      }
      await getData(this.contactPersonUrl, this.contactPersonQarams).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
          }
          arrList.push(arr)
        }
        this.contactPersonOptions = arrList
        this.listLoading = false
      })
    },

    // 遠程搜索 貨幣
    remoteMethod_currencyOptions() {
      this.getRelation(this.currencytUrl).then((result) => {
        const res = result
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            remark: res[index].remark
          }
          arrList.push(arr)
        }
        this.currencyOptions = arrList
      })
    },
    // 遠程搜索 付款方式
    remoteMethod_paymentOptions() {
      this.getRelation(this.paymentUrl).then((result) => {
        const res = result
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            remark: res[index].remark
          }
          arrList.push(arr)
        }
        this.paymentOptions = arrList
      })
    },
    // 遠程搜索 產品
    remoteMethod_product(query) {
      if (query !== '') {
        this.getRelation(this.productUrl, query).then((result) => {
          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              num: 1,
              price: res[index].price,
              cost: res[index].cost,
              describe: res[index].describe,
              // 產品分類
              product_category: res[index].product_category.name,
              product_category_id: res[index].product_category.id,
            }
            arrList.push(arr)
          }
          this.productOptions = arrList
        })
      } else {
        this.getRelation(this.productUrl).then((result) => {

          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              price: res[index].price,
              cost: res[index].cost,
              describe: res[index].describe,
              // 產品分類
              product_category: res[index].product_category.name,
              product_category_id: res[index].product_category.id,
            }
            arrList.push(arr)
          }
          this.productOptions = arrList
        })
      }

      console.log(this.productOptions)
    },
    changeCompany(item) {
      this.$set(this.form, "address", item.address)
      // 重新選擇公司後清空聯繫人
      this.$set(this.form, "contact_person", '')
      console.log(item);
      // 獲取聯繫人
      this.remoteMethod_contactPersonOptions("", item.value)
    },

    changePayment(item) {
      this.$set(this.form, "payment_detail", item.remark)
    },

    changeClause(item) {
      this.$set(this.form, "clause_detail", item.detail)
    },
    changeContent(item, index) {
      console.log('item', item.label);
      console.log('i', index);
      // this.$set(this.form.content, [index], item)
      this.$set(this.form.content, index,
        {
          "value": item.label.value,
          "label": item.label.label,
          "num": item.label.num,
          "price": item.label.price,
          "cost": item.label.cost,
          "describe": item.label.describe,
          "product_category": item.label.product_category,
          "product_category_id": item.label.product_category_id
        })
      console.log(this.form.content[index]);
    },

    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = this.$t('quotation.total');
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (index === 5) {
            this.form.allPrice = sums[index];
            sums[index] = this.$options.filters['getArea'](sums[index]);
          }
          else if (index === 4) {
            sums[index] = this.$t('quotation.totalPrice');
          }
          else if (index === 6) {
            this.form.allCost = sums[index];
            console.log(this.form.allCost);
            sums[index] = this.$options.filters['getArea'](sums[index]);
          }

        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    clearContent() {
      for (let index = 0; index < this.form.content.length; index++) {
        const element = this.form.content[index];
        // 如果产品名称为空，则删除这个数据
        if (!element.label) {
          this.deleteItem(index);
        }
      }
    },
    handleDialogClose() {

      this.getList(this.listQuery)
      this.dialogTableVisible = false
    },
    // 子傳父
    getDialogVisble(data) {
      this.dialogFormVisible = data
      this.getList(this.listQuery)
    },
    // 獲取id來組成UUid
    async getUid(type) {
      this.listQuery = {
        page: 1,
        pageSize: 1,
        sort: 'id:DESC',
      }
      if (type === 'add') {
        let uidArr = this.form.uuid.split('-')
        let uid = uidArr[1]
        this.listQuery = {
          page: 1,
          pageSize: 1,
          sort: 'id:DESC',
          _q: uid
        }
        console.log(this.listQuery);
      }
      await getData(this.baseUrl, this.listQuery).then(response => {
        const res = response.results
        const arrList = []
        // 如果沒有單據，則從01開始新增
        if (res[0]) {
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const arr = {
              // id
              id: res[index].id,
              uuid: res[index].uuid,

            }
            arrList.push(arr)
          }
          let list = arrList[0]
          if (type === 'add') {
            console.log(list.uuid);
            let numArr = list.uuid.split('-')
            let num = parseInt(numArr[2]) + 1
            this.uuid = 'Q-' + numArr[1] + '-' + fillZero(num, 2)
          } else {
            let numbers = list.uuid.match(/\d+/g);
            let num = parseInt(numbers[0])
            this.uuid = 'Q-' + fillZero(num + 1, 5) + "-01"
          }

        } else {
          this.uuid = 'Q-00128-01'
        }
      })
    },

    // 删除的最后确认按钮
    dialogConfirm(type) {
      this.$confirm(this.$t('table.goonPrompt'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        switch (type) {
          case 'create':
            this.createData()
            break;
          case 'add':
            this.createData('add')
            break;
          case 'updata':
            this.updateData()
            break;

          default:
            break;
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.cancelPrompt')
        })
      })
    },

    // 失去焦点金额格式化
    inputMoney(el) {
      let temp = Number(el.target.value) || null;
      this.totalprice = this.priceFormat(temp)
    },
    // 获得焦点金额去掉格式
    uninputMoney(el) {
      if (!!el.target.value) {
        this.totalprice = this.delcommafy(el.target.value)
      } else {
        this.totalprice = null
      }
    },

    //去除千分位中的‘，’
    delcommafy(num) {
      if (!num) return num;
      num = num.toString();
      num = num.replace(/,/gi, '');
      if (num.indexOf('.00') > 0) num = parseInt(num);
      return num;
    }

  },

}

</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: center;
}

.app-container {
  position: relative;
}

.other {
  position: absolute;
  top: 0;
  right: 20px;

  .otherTable {
    margin-top: 20px;
    border: 1px solid #dfe6ec;
    color: #909399;
    font-size: 14px;

    .title {
      display: block;
      font-size: 14px;
      font-weight: bold;
      text-align: center;

      span {
        display: inline-block;
        width: 50%;
        height: 48px;
        line-height: 48px;
        border-bottom: 1px solid #dfe6ec;
        border-right: 1px solid #dfe6ec;

        &:last-child {

          border-right: none;
        }
      }
    }

    .number {
      div {
        display: inline-block;
        width: 50%;
        text-align: center;
        border-right: 1px solid #dfe6ec;

        span {
          display: inline-block;
          line-height: 40px;
        }

        &:last-child {

          border-right: none;
        }
      }
    }
  }

  .download {
    margin-top: 20px;
  }
}

.cost {
  color: red;
}

.describe {
  margin-top: 10px;
}
</style>
<style scoped>
/deep/.el-table .el-table__footer-wrapper :nth-child(7) {
  color: red;
}
</style>
