// requestCms.js
import axios from 'axios';

// const baseUrl = 'http://127.0.0.1:3000';
const baseUrl = 'https://yohomall.esc-map.com';

async function getDataCms(url, param) {
  const apiUrl = `${baseUrl}/${url}`;
  console.log(apiUrl);
  try {
    const response = await axios.get(apiUrl, { params: param });
    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
    return null;
  }
}

export { getDataCms };
