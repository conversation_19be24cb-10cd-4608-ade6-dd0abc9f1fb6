<template>
  <div class="app-container">
    <h1>流水統計</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item label="">
        <el-date-picker v-model="queryDate" type="daterange" range-separator="至" start-placeholder="開始日期"
          end-placeholder="結束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
        <!-- <el-button icon="el-icon-plus" type="primary" @click="newClick()">新增</el-button> -->
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe show-summary :summary-method="getSummaries" @sort-change="handlesortChange">
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-table v-loading="listLoading" :data="props.row.content" border fit highlight-current-row
            style="width: 1400px">

            <el-table-column align="center" label="序号" min-width="30px">
              <template slot-scope="scope">
                <span>{{ (scope.$index + 1) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="收款日期" width="100" />
            <el-table-column prop="cost" label="添加成本" width="" />
            <el-table-column prop="costRemark" label="說明" width="" />
            <el-table-column prop="receive" label="已收" width="" />
            <el-table-column prop="payment" label="收款方法" width="" />
            <el-table-column prop="createBy" label="收款人" width="" />
            <el-table-column :label="$t('table.operate')" width="200">
              <template slot-scope="scope">
                <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope)">編輯</el-button>
                <el-popconfirm confirm-button-text="取消" cancel-button-text="删除" icon="el-icon-info" icon-color="red"
                  title="这确定删除吗？" @onCancel="deleteDialog(scope)">
                  <el-button slot="reference" type="danger" size="small" icon="el-icon-delete"
                    style="margin-left:12px">删除
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column prop="quotation.uuid" label="報價單號" width="120" sortable="custom" />
      <el-table-column prop="bill.uuid" label="發票單號" width="110" sortable="custom" />
      <el-table-column prop="company.name" :label="$t('table.companyName')" width="180" sortable="custom" />
      <el-table-column prop="createdAt" label="创建日期" width="100">
        <template slot-scope="{row}">
          <template>
            <span>{{ row.createdAt.substring(0, 10) }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="allPrice" label="总价" width="100">
        <template slot-scope="{row}">
          <span>{{ (row.allPrice = row.quotation.allPrice) | getArea }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="allCost" label="最初成本" width="100">
        <template slot-scope="{row}">
          <span>{{ (row.allCost = row.quotation.allCost) | getArea }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="finalCost" label="最终成本" width="100">
        <template slot-scope="{row}">
          <span>{{ row.finalCost | getArea }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="profit" label="總利潤" width="100">
        <template slot-scope="{row}">
          <span>{{ row.profit | getArea }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="allReceive" label="總共已收" width="100">
        <template slot-scope="{row}">
          <span>{{ row.allReceive | getArea }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="arrears" label="欠收" width="100">
        <template slot-scope="scope">
          <template>
            <span>{{ scope.row.arrears = scope.row.quotation.allPrice ?
                scope.row.quotation.allPrice - scope.row.allReceive :
                '' | getArea
            }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="receiveTimes" label="收款次數" width="100">
        <template slot-scope="scope">
          <template>
            <span>{{ scope.row.receiveTimes = scope.row.content.length ?
                scope.row.content.length :
                0
            }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="160">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleCreate(scope.row)">新增收款</el-button>

        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] + '收款'" :visible.sync="dialogFormVisible" width="70%">

      <el-table v-loading="listLoading" :data="content" border fit highlight-current-row style="width: 1400px">

        <el-table-column align="center" min-width="70px" label="報價單ID">
          <template slot-scope="{row}">
            <template>
              <span>{{ row.quotationId }}</span>
            </template>
          </template>
        </el-table-column>

        <el-table-column align="center" min-width="150px" label="收款時間">
          <template slot-scope="{row}">
            <template>

              <el-date-picker v-model="row.date" type="date" placeholder="选择日期" style="width:200px">
              </el-date-picker>
            </template>
          </template>
        </el-table-column>



        <el-table-column align="center" min-width="120px" label="公司">
          <template slot-scope="{row}">
            <template>
              <span>{{ row.name }}</span>
            </template>
          </template>
        </el-table-column>

        <el-table-column align="center" prop="cost" min-width="70px" label="添加成本">
          <template slot-scope="{row}">
            <template>
              <el-input type="number" v-model="row.cost" size="small" />
            </template>
          </template>
        </el-table-column>

        <el-table-column align="center" prop="costRemark" min-width="120px" label="說明">
          <template slot-scope="{row}">
            <template>
              <el-input type="textarea" v-model="row.costRemark" size="small" />
            </template>
          </template>
        </el-table-column>


        <el-table-column align="center" prop="receive" min-width="70px" label="收款">
          <template slot-scope="{row}">
            <template>
              <el-input type="number" v-model="row.receive" size="small" />
            </template>
          </template>
        </el-table-column>

      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? '新增收款' : '修改收款' }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增流水統計" :visible.sync="dialogTableVisible" width="90%">
      <otherTable :childEvent="getDialogVisble" :tableId="tableId" :dialogState="dialogTableStatus" :key="time" />
    </el-dialog>


  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'
import otherTable from '@/components/otherTable'

export default {
  name: 'Turnover',
  components: { Pagination, otherTable },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::statistical.statistical/',
      companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      meUrl: '/admin/users/me/',
      list: [],
      totle: 0,
      // 登錄的用戶、操作的用戶
      userName: '',
      listLoading: false,
      loading: false,
      // 弹框
      dialogFormVisible: false,
      // table弹框
      dialogTableVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      // 父子table傳值
      tableId: '',
      time: '',
      // content
      content: [],
      queryDate: [],
      // content
      contentIndex: 0,
      // 修改數據
      formData: [],
      // 公司
      companyOptions: [],
      // 职位
      positionOptions: [],
      form: {},
      formRow: {},
      // 弹框属于發票還是發貨單的列表
      dialogTableStatus: '',
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'quotation.id:DESC',
        _q: ''
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 职位
        position: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 狀態
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList(this.listQuery)
  },
  methods: {
    // 時間轉換函數
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        };
      };
      return fmt;
    },
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.results
        this.list = res
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字
    async getRelation(url, p) {
      let params = ''
      let rt
      if (p) {
        params = {
          'filters[$and][1][name][$contains]': p
        }
      }
      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name
          }
          arrList.push(arr)
        }
        this.listLoading = false
        rt = arrList
      })
      return rt
    },
    // 點擊修改的事件
    handleClick(scope) {
      console.log(scope.row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.content = [{
        id: scope.row.id,
        quotationId: scope.row.quotation.id,
        name: scope.row.company.name,
        date: this.dateFormat("YYYY-mm-dd", new Date()),
        cost: scope.row.cost ? scope.row.cost : "",
        costRemark: scope.row.costRemark ? scope.row.costRemark : "",
        receive: scope.row.receive ? scope.row.receive : ""
      }]
      this.contentIndex = scope.$index

    },
    // 確認修改的事件
    updateData() {
      this.varyContent('update', this.content[0], this.contentIndex)
      this.dialogFormVisible = false
    },
    // 创建的按钮打开
    handleCreate(row) {
      this.listLoading = true
      this.content = [{
        id: row.id,
        quotationId: row.quotation.id,
        name: row.company.name,
        date: this.dateFormat("YYYY-mm-dd", new Date()),
        cost: "",
        costRemark: "",
        receive: ""
      }]
      this.dialogStatus = 'create'
      // this.dialogStatus = 'update'
      this.dialogFormVisible = true
      console.log(row)
      this.listLoading = false
    },
    // 提交創建的事件
    createData() {
      this.varyContent('create', this.content[0], 0)
      this.dialogFormVisible = false
    },
    // 删除的最后确认按钮
    deleteDialog(scope) {
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        this.varyContent('delete', scope.row, scope.$index)
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },
    async varyContent(type, row, index) {
      const postForm = {}
      let params = {
        'filters[$and][0][id][$eq]': row.id
      }
      // 獲取用戶信息
      await getData(this.meUrl, '').then(response => {
        const res = response.data
        const name = res.firstname
        this.userName = name

      })
      await getData(this.baseUrl, params).then(response => {
        const res = response.results
        this.formData = res[0]

        // 如果有index，則改變content
        if (type === 'delete') {
          this.formData.content.splice(index, 1)
        } else if (type === 'update') {
          this.formData.content[index] = {
            "id": row.id,
            "date": row.date,
            "cost": row.cost,
            "costRemark": row.costRemark,
            "receive": row.receive,
            "quotation": {
              "id": this.formData.quotation.id
            },
            "company": {
              "name": this.formData.company.name
            },
            "createBy": this.userName
          }
        } else if (type === 'create') {
          let newRow = {
            "id": row.id,
            "date": row.date,
            "cost": row.cost,
            "costRemark": row.costRemark,
            "receive": row.receive,
            "quotation": {
              "id": this.formData.quotation.id
            },
            "company": {
              "name": this.formData.company.name
            },
            "createBy": this.userName
          }
          this.formData.content.push(newRow)
        }
        // 計算屬性，計算並返回修改數據
        let newData = this.mathData(this.formData)
        const tempData = Object.assign({}, newData)

        postData(this.baseUrl, 'put', row.id, tempData).then(() => {
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Update Successfully',
            type: 'success',
            duration: 1000
          })
        })
      })
    },
    mathData(data) {
      let allOtherCost = 0 // 總添加成本
      let allReceive = 0 // 總收款金額

      let finalCost = 0 // 最終成本
      let profit = 0 // 總利潤
      let allPrice = data.quotation.allPrice // 總價
      let allCost = data.quotation.allCost // 總成本
      let content = data.content
      for (let index = 0; index < content.length; index++) {
        const element = content[index];
        let cost = element.cost ? parseFloat(element.cost) : 0
        let receive = element.receive ? parseFloat(element.receive) : 0
        allOtherCost += cost
        allReceive += receive
      }
      // 最終成本 = 總成本 + 其他添加成本
      finalCost = allCost + allOtherCost
      // 總利潤 = 總價 - 最終成本
      profit = allPrice - finalCost

      let newData = {
        finalCost: finalCost,
        profit: profit,
        allReceive: allReceive,
        content: content
      }
      return newData
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合計';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (index === 11) {
            sums[index] = '';
          } else {
            sums[index] = this.$options.filters['getArea'](sums[index]);
          }

        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      // 如果選擇了時間，則把時間組合到搜索語句裡面去
      if (this.queryDate) {
        this.listQuery['filters[$and][0][createdAt][$gt]'] = this.timeToTimestamp(this.queryDate[0])
        this.listQuery['filters[$and][0][createdAt][$lt]'] = this.timeToTimestamp(this.queryDate[1])
      }
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 20,
        sort: 'quotation.id:DESC',
        _q: ''
      },
        this.queryDate = []
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },
    changeCountry(item) {
      this.form.currency = item.currency
    },
    // 新增或者修改时，新增电话或者手机的方法
    add(t) {
      if (t === 'telephone') {
        this.form.telephone.push('')
      } else if (t === 'phone') {
        this.form.phone.push('')
      }
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(t, delete_index) {
      if (t === 'telephone') {
        this.form.telephone.splice(delete_index, 1)
      } else if (t === 'phone') {
        this.form.phone.splice(delete_index, 1)
      }
    },

    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(delete_index) {
      this.form.content.splice(delete_index, 1)
    },

    // 排序的方法
    handlesortChange(column) {
      let target = ''
      switch (column.prop) {
        case 'quotation':
          target = 'quotation.uuid'
          break;
        case 'bill':
          target = 'bill.uuid'
          break;
        case 'company':
          target = 'company.name'
          break;
        case 'uuid':
          target = 'uuid'
          break;

        default:
          break;
      }
      if (column.order == "ascending") {
        this.listQuery.sort = target + ":ASC"
      }
      if (column.order == "descending") {
        this.listQuery.sort = target + ":DESC"
      }
      this.getList(this.listQuery)
    },
    // 新增流水統計
    // newClick() {
    //   this.listLoading = true
    //   this.time = new Date().getTime()
    //   this.dialogTableStatus = 'bill'
    //   this.tableId = '999'
    //   console.log(this.dialogTableStatus);
    //   this.dialogTableVisible = true
    //   this.listLoading = false
    // },
    // 子傳父
    getDialogVisble(data) {
      this.dialogFormVisible = data
      this.getList(this.listQuery)
    },
    // 把時間轉化為時間戳
    timeToTimestamp(time) {
      let timestamp = Date.parse(new Date(time).toString());
      //timestamp = timestamp / 1000; //时间戳为13位需除1000，时间戳为13位的话不需除1000
      // console.log(time + "的时间戳为：" + timestamp);
      let d = new Date(timestamp);
      // console.log("dddd1111",d)
      d = d.toISOString()
      return d;
      //2021-11-18 22:14:24的时间戳为：1637244864707
    }


  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}
</style>
