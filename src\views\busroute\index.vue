<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1>{{ $t('route.busroute') }}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">

      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">

      <el-table-column prop="rank" label="順序" width="80" />
      <el-table-column prop="name" label="路線名稱" width="" />
      <!-- <el-table-column prop="pdf_type" label="通告類型" width="">
        <template slot-scope="{row}">
          <el-tag>{{ row.pdf_type ? row.pdf_type.name_tc : "草稿" }}</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-form-item label="路線名稱" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="順序" prop="rank">
          <el-input v-model="form.rank" />
        </el-form-item>

        <p class="route-title">線路站點（英文）</p>
        <el-form-item v-for="(item, i) of form.route" :label="'站點' + (i + 1)">
          <el-input v-model="form.route[i]" />
          <div v-if="i > 0" class="delete">
            <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem('route', i)" />
          </div>
          <div v-if="i === form.route.length - 1" class="add">
            <el-button icon="el-icon-circle-plus" type="primary" @click="add('route')">{{
              $t('quotation.addRow')
            }}</el-button>
          </div>
        </el-form-item>

        <p class="route-title">線路站點（繁體）</p>
        <el-form-item v-for="(item, i) of form.route_tc" :label="'站點' + (i + 1)">
          <el-input v-model="form.route_tc[i]" />
          <div v-if="i > 0" class="delete">
            <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem('route_tc', i)" />
          </div>
          <div v-if="i === form.route_tc.length - 1" class="add">
            <el-button icon="el-icon-circle-plus" type="primary" @click="add('route_tc')">{{
              $t('quotation.addRow')
            }}</el-button>
          </div>
        </el-form-item>

        <p class="route-title">線路站點（簡體）</p>
        <el-form-item v-for="(item, i) of form.route_zh" :label="'站點' + (i + 1)">
          <el-input v-model="form.route_zh[i]" />
          <div v-if="i > 0" class="delete">
            <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem('route_zh', i)" />
          </div>
          <div v-if="i === form.route_zh.length - 1" class="add">
            <el-button icon="el-icon-circle-plus" type="primary" @click="add('route_zh')">{{
              $t('quotation.addRow')
            }}</el-button>
          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import axios from "axios"
import { getData, postData, uploadData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: '',
      apiName: "bus-route",
      listLoading: false,
      uploadLoading: false,
      btnLoading: false,
      file: "",
      fileName: "",
      fileId: "",
      fileUploadType: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '0',
        label: "草稿"
      }, {
        value: '1',
        label: "通告"
      }, {
        value: '2',
        label: "會所"
      }, {
        value: '3',
        label: "業委會資訊"
      }, {
        value: '4',
        label: "其他"
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'rank:ASC',
        _q: ''
      },

      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_tc: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_zh: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      this.listLoading = true
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    /*
    點擊修改的事件
    */
    handleClick(row) {
      // this.listLoading = true
      this.resetForm
      this.dialogStatus = 'update'
      this.dialogFormVisible = true

      this.formRow = {
        id: row.id,
        name: row.name,
        rank: row.rank,
        route: row.route,
        route_tc: row.route_tc,
        route_zh: row.route_zh,
      }
      this.form = Object.assign({}, this.formRow) // copy obj
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let form = this.form
          console.log(this.formRow);

          console.log(this.form);
          // 设定pdf的id
          const tempData = Object.assign({}, form)
          console.log(tempData)
          postData(this.apiName, 'put', tempData.id, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    // 新增或者修改时，新增电话或者手机的方法
    add(t) {
      this.form[t].push('')
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(t, delete_index) {
      this.form[t].splice(delete_index, 1)
    },
    /*
    清空提交表
    */
    resetForm() {
      this.form = {
        name: "",
        rank: "",
        route: [""],
        route_tc: [""],
        route_zh: [""],
      }
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          // 设定通告的类型
          const temp = this.form
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          // 按鈕開始加載
          this.btnLoading = true
          postData(this.apiName, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            // 按鈕停止加載並關閉彈框
            this.btnLoading = false
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })

        }
      })
    },

    /*
    管理關聯表表的方法
    */
    connectFunc(newId, oldId) {
      if (newId) {
        if (oldId) {
          if (oldId == newId) {
            return {
              "disconnect": [],
              "connect": []
            }
          } else {

            return {
              "disconnect": [
                {
                  "id": parseInt(oldId)
                }],
              "connect": [
                {
                  "id": parseInt(newId),
                  "position": {
                    "end": true
                  }
                }
              ]
            }
          }

        } else {
          return {
            "disconnect": [],
            "connect": [
              {
                "id": parseInt(newId),
                "position": {
                  "end": true
                }
              }
            ]
          }
        }

      } else if (oldId) {
        return {
          "disconnect": [
            {
              "id": parseInt(oldId),
              "position": {
                "end": true
              }
            }],
          "connect": []
        }
      }
      else {
        return {
          "disconnect": [],
          "connect": []
        }
      }


    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.apiName, "DELETE", row.id, '').then(() => {
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}

.route-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-left: -160px;
}
</style>
