import {
  string
} from "clipboard"

// 返回關係的狀態（修改）
export function relationReturn(newId, oldId) {
  // 如果有改變，則返回數據，否則返回空
  if (newId && oldId && newId != oldId) {
    const re = {
      connect: [{
        id: newId
      }],
      disconnect: [{
        id: oldId
      }]
    }
    return re
  } else if (newId && !oldId && newId != oldId) {
    const re = {
      connect: [{
        id: newId
      }],
      disconnect: []
    }
    return re
  } else {
    const re = {
      connect: [],
      disconnect: []
    }
    return re
  }
}

// 返回關係的狀態（新增）
export function relationCreateReturn(data) {
  // 如果有改變，則返回數據，否則返回空
  if (data) {
    const re = {
      connect: [{
        id: data
      }],
      disconnect: []
    }
    return re
  } else {
    const re = {
      count: 0
    }
    return re
  }
}


// 補零函數
export function fillZero(str, num) {
  var realNum;
  let st = str.toString()
  if (st.length < num) {
    let a = num - st.length;
    for (let index = 0; index < a; index++) {
      st = '0' + st
    }
    realNum = st;
  } else {
    realNum = str;
  }
  return realNum;
}
