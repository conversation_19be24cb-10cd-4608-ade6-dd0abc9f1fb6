<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1> Shop </h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="img" label="圖片" width="140">
        <template slot-scope="{row}">
          <a :href='baseURL + row.img.url' target="_blank">
            <img class="avatar" :src="baseURL + (row.img.formats ? row.img.formats.thumbnail.url : row.img.url)" alt="">
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名稱（英文）" width="" />
      <el-table-column prop="name_tc" label="名稱（繁體）" width="" />
      <el-table-column prop="num" label="店鋪號" width="" />
      <el-table-column prop="floor" label="樓層" width="" />
      <el-table-column prop="mall" label="Mall" width="" />
      <el-table-column prop="shoptype" label="類型" width="140">
        <template slot-scope="{row}">
          {{ row.shoptype.name }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-form-item label="名稱（英文）" prop="name">
          <el-input v-model="form.name" @input="generateInitials" />
        </el-form-item>
        <el-form-item label="名稱（繁體）" prop="name_tc">
          <el-input v-model="form.name_tc" />
        </el-form-item>
        <el-form-item label="名稱（簡體）" prop="name_zh">
          <el-input v-model="form.name_zh" />
        </el-form-item>
        <!-- <el-form-item label="簡介（英文）" prop="description">
          <el-input type="textarea" :autosize="true" v-model="form.description" />
        </el-form-item>
        <el-form-item label="簡介（繁體）" prop="description_tc">
          <el-input type="textarea" :autosize="true"  v-model="form.description_tc" />
        </el-form-item>
        <el-form-item label="簡介（簡體）" prop="description_zh">
          <el-input type="textarea" :autosize="true" v-model="form.description_zh" />
        </el-form-item> -->

        <el-form-item label="門牌號" prop="num">
          <el-input v-model="form.num" />
        </el-form-item>

        <el-form-item label="楼层" prop="floor">
          <el-select v-model="form.floor" class="filter-item" placeholder="Please select">
            <el-option v-for="item in floorOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="Mall" prop="mall">
          <el-select v-model="form.mall" class="filter-item" placeholder="Please select">
            <el-option v-for="item in ['YOHO MALL I','YOHO MALL II','YOHO MALL MIX']" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="首字母" prop="initials">
          <el-input v-model="form.initials" />
        </el-form-item> -->

        <!-- <el-form-item label="地圖ID" prop="modelId">
          <el-input v-model="form.modelId" :disabled="true" />
          <el-button @click="dialogMapVisible = true">
            選點
          </el-button>
        </el-form-item> -->

        <el-form-item label="類型" style="margin-top:10px" prop="shoptype">
          <el-select v-model="form.shoptype" class="filter-item" placeholder="Please select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>


        <!-- 圖片 -->
        <image-uploader upload-url="/upload/" upload-type="img" upload-name="image" @upload-success="handleUploadSuccess"
          :reset-trigger="resetTrigger"></image-uploader>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog width="80%" title="選點" :visible.sync="dialogMapVisible">
      <!-- <h2>{{ modelId }}</h2> -->
      <div>
        <el-button @click="dialogMapVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="modelFunc">
          確認
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData, uploadData } from '@/api/requestData'
import Pagination from '@/components/Pagination'
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

export default {
  name: 'Product',
  components: { Pagination, ImageUploader },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: "shop", // 变量修改，请求主题名称
      listLoading: false,
      btnLoading: false,

      modelId: "",
      // 弹框
      dialogFormVisible: false,
      // 地圖彈框
      dialogMapVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },
      statusOptions: [],
      // 楼层选项
      floorOptions: [
        {
          value: "1",
          label: "GF"
        }, {
          value: "2",
          label: "L1"
        }, {
          value: "3",
          label: "L2"
        }, {
          value: "4",
          label: "L3"
        },
      ],

      resetTrigger: 0, // 控制重置逻辑的状态变量 ImageUploader
      // 变量修改，表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_tc: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_zh: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        num: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        floor: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        mall: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],

        initials: [
          { required: true, message: '請輸入單個英文字母', trigger: 'blur' },
          {
            validator: this.validateLetter,
            trigger: 'blur'
          }
        ],
        shoptype: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    validateLetter(rule, value, callback) {
      if (/^[A-Za-z]{1}$/.test(value)) {
        callback();
      } else {
        callback(new Error('請輸入單個英文字母'));
      }
    },

    /*
    上傳文件前的处理 ImageUploader
    */
    handleUploadSuccess({ id, type }) {
      // 使用传递的type动态更新相应的状态
      console.log(`${type}:`, id);
      this.form[`${type}`] = id;
      console.log(this.form);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    /*
    點擊修改的事件
    */
    handleClick(row) {
      // this.listLoading = true

      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      // 变量修改时的参数
      this.formRow = {
        id: row.id,
        name: row.name,
        name_tc: row.name_tc,
        name_zh: row.name_zh,
        floor: row.floor,
        mall: row.mall,
        num: row.num,
        initials: row.initials,
        shoptype_id: row.shoptype.id,
        shoptype_name: row.shoptype.name,
        shoptype: {
          value: row.shoptype.id + '',
          label: row.shoptype.name_tc
        },
        modelId: row.modelId,
        // img: row.img.url,
        // description: row.description,
        // description_tc: row.description_tc,
        // description_zh: row.description_zh,
      }

      this.form = Object.assign({}, this.formRow) // copy obj
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let form = this.form
          let shoptype = this.connectFunc(parseInt(this.form.shoptype.value), parseInt(this.formRow.shoptype.value))

          // 重设floor，变量修改floor
          let floor = this.form.floor.label
          this.form.floor = floor

          this.form.shoptype = shoptype

          if (this.fileId) {
            // 设定pdf的id
            this.form.img = parseInt(this.fileId)

          }

          const tempData = Object.assign({}, form)
          console.log(tempData)
          postData(this.apiName, 'put', tempData.id, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    清空提交表
    */
    resetForm() {
      getData("shoptype", this.listQuery).then(response => {
        const res = response.results
        let option = []
        for (const item of res) {
          option.push({
            value: item.id.toString(), // 将id转换为字符串
            label: item.name_tc
          });
        };
        this.statusOptions = option
        this.listLoading = false
      })
      // 变量修改，清零配置
      this.form = {
        name: "",
        name_tc: "",
        name_zh: "",
        floor: "",
        mall: "",
        num: "",
        initials: "",
        modelId: "",
        shoptype_id: "",
        shoptype_name: "",
        shoptype: {}
      }
      // 文件上傳部分的清零
      this.file = ""
      this.fileId = ""
      this.fileName = ""
      this.fileUploadType = false
      // 清空組件狀態 ImageUploader
      this.triggerReset()
    },
    // ImageUploader 重置器
    triggerReset() {
      this.resetTrigger += 1; // 每次调用时改变值，触发ImageUploader的重置
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          if (this.form.img) {

            // 如果状态为0，则为空，否则就有内容，变量修改type
            let pdf_type = this.connectFunc(parseInt(this.form.shoptype.value), '')

            // 重设pdf_type，变量修改type
            this.form.shoptype = pdf_type
            // 重设floor，变量修改floor
            let floor = this.form.floor.label
            this.form.floor = floor

            // 设定通告的类型
            const temp = this.form
            const tempData = Object.assign({}, temp)
            console.log(tempData)
            // 按鈕開始加載
            this.btnLoading = true
            postData(this.apiName, 'post', '', tempData).then(() => {
              this.getList(this.listQuery)
              // 按鈕停止加載並關閉彈框
              this.btnLoading = false
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Create Successfully',
                type: 'success',
                duration: 1000
              })
            })
          } else {
            this.$notify({
              title: 'Warning',
              message: '請先上傳文件',
              type: 'success',
              duration: 1000
            })

          }
        }
      })
    },

    /*
    管理關聯表表的方法
    */
    connectFunc(newId, oldId) {
      if (newId) {
        if (oldId) {
          if (oldId == newId) {
            return {
              "disconnect": [],
              "connect": []
            }
          } else {

            return {
              "disconnect": [
                {
                  "id": parseInt(oldId)
                }],
              "connect": [
                {
                  "id": parseInt(newId),
                  "position": {
                    "end": true
                  }
                }
              ]
            }
          }

        } else {
          return {
            "disconnect": [],
            "connect": [
              {
                "id": parseInt(newId),
                "position": {
                  "end": true
                }
              }
            ]
          }
        }

      } else if (oldId) {
        return {
          "disconnect": [
            {
              "id": parseInt(oldId),
              "position": {
                "end": true
              }
            }],
          "connect": []
        }
      }
      else {
        return {
          "disconnect": [],
          "connect": []
        }
      }


    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.apiName, "DELETE", row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    // 获取首字母的方法
    generateInitials() {
      const name = this.form.name.trim();
      if (name === '') {
        this.form.initials = '';
      } else if (/^[A-Za-z]/.test(name)) {
        this.form.initials = name.charAt(0).toUpperCase();
      } else {
        this.form.initials = '1';
      }
    },

    // map的数据返回
    handleValueChanged(value) {
      this.modelId = value;
    },
    // 賦值modelId的方法
    modelFunc() {
      this.dialogMapVisible = false
      this.form.modelId = this.modelId
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}
</style>
