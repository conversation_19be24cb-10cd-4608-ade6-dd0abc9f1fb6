<template>
  <div class="dashboard-container">
    <!-- <component :is="currentRole" /> -->

    <img class="img" src="./welcome.jpg" alt="">
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import adminDashboard from './admin'
import editorDashboard from './editor'

export default {
  name: 'Dashboard',
  components: { adminDashboard, editorDashboard },
  data() {
    return {
      currentRole: 'adminDashboard'
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  created() {
    if (!this.roles.includes('admin')) {
      this.currentRole = 'editorDashboard'
    }
  }
}
</script>

<style scoped  lang="scss">
.img{
  width: 1000px;
  margin: 15px;
}
</style>
