<template>
  <div>
    <div class="toolBarDiv"></div>
    <div id="fengmap">

      <div class="mask"
        @click="moveMap(element.floor, element.modelId, element.name, element.name_tc, element.name_zh, element.num, element.image)">
        Eshowcase</div>
      <div id="msg_box"></div>

    </div>
    <div class="dialog"></div>
  </div>
</template>

<script>
const baseUrl = "./"
export default {
  data() {
    return {
      map: null,
      navi: null,
      analyser: null,
      floorInfo: null,
      start: null,
      dest: null,
      model: null,
      clickCount: 0,
      activeBtn: 'start',
      activeSpeed: 'normal',
      startPoint: [12027201.957725214, 2627218.783429671],
      destPoint: [12027132.478464777, 2627250.5381198213],
      floor: null,
      floorData1: null,
      floorData2: null,
      oldModel: "",
      imageMarker: null,
      imageMarkerBox: [],
      screenWidth: 0,// 屏幕宽度
      mapZoom: 19.8, // 地图缩放等级
      list: [],
      element: null,
      simulateOptions: {
        speed: 20,
        followPosition: false,
        followAngle: false,
        changeTiltAngle: false,
        zoom: 19,
        maxZoom: 19,
      },
      isPlaying: false,
      options: {
        container: null,
        appName: 'onenorth',
        key: '92241bf53e6b93043ab712337c8d3668',
        mapID: '1666241099991822337',
        themeID: '1666287548565934081',
        mapURL: './data/',
        themeURL: './data/theme/',
        level: 1,
        mapZoom: 19,
        tiltAngle: 42,
        rotation: 330,
        floorSpace: 100,
        visibleLevels: [1],
        // appName: 'test',
        // key: '4c3b5af07eab7b4bc2523eba3e15439e',
        // mapID: '1675106573009842177',
        // themeID: '1675155633502642178',
        // level: 1,
        // mapZoom: 18.5,
        // tiltAngle: 36,
        // // rotation: 310,
        // floorSpace: 150,
        // visibleLevels: [1, 2, 3],
      },
      lineOptions: {
        color: "red",
        width: "6",
        radius: "3",
      },
      indexFloor: null,
    }
  },
  mounted() {
    // Your initialization code here
    this.options.container = document.getElementById('fengmap');
    this.indexFloor = this.options.level;
    this.map = new fengmap.FMMap(this.options);
    document.querySelector("#fengmap > canvas").style.left = "0";
    this.map.on('loaded', () => {
      this.analyser = new fengmap.FMNaviWalkAnalyser({
        map: this.map
      }, () => {
        // this.naviRoute();
      });
      this.floorInfo = this.map.getFloorInfos();
    });
    this.map.on('click', (e) => {
      let target = e.targets[0];
      console.log(e.coords.x, e.coords.y);
      let modelId = target?.FID || ''
      console.log(modelId);
      this.sendValueToParent(modelId)
    });
    this.map.on('loaded', () => {
      var scrollFloorCtlOpt = {
        position: fengmap.FMControlPosition.RIGHT_TOP,
        floorButtonCount: 5,
        offset: {
          x: -20,
          y: 80
        },
        viewModeControl: true,
        floorModeControl: true,
        needAllLayerBtn: true
      };

      var scrollFloorControl = new fengmap.FMToolbar(scrollFloorCtlOpt);
      scrollFloorControl.addTo(this.map);




    });
    // 初始化起点和终点


  },
  methods: {


    getFloorNameByGid(level) {
      var gname = this.floorInfo.find((item) => {
        return item.level === level;
      }).name;
      return gname;
    },
    sendValueToParent(num) {
      this.$emit('value-changed', num); // 12345 是要传递给父组件的值
    }


  },
}
</script>

<style lang="less">
#fengmap {
  position: relative;
  width: 100%;
  // max-width: 820px;
  height: 600px;
}

.toolBarDiv {
  display: none;
}

.mask {
  position: absolute;
  width: 12vw;
  height: 6vh;
  bottom: 0;
  right: 0;
  background: #fff;
  z-index: 999;
  text-align: center;
  line-height: 6vh;
  font-size: 3vh;
  border-radius: 15px 0 0 0;
  color: #dc7883;
}
</style>
