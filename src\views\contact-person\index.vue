<template>
  <div class="app-container">
    <!-- <h1>聯繫人</h1> -->
    <h1>{{ $t('route.contacts') }}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item label="">
        <el-input v-model="listQuery['filters[$and][0][company][name][$contains]']"
          :placeholder="$t('table.queryCompany')" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">{{ $t('table.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">{{ $t('table.reset') }}</el-button>

        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
        <el-button type="primary" plain icon="el-icon-download" @click="handleDownload()">Export Excel</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe @sort-change="handlesortChange">
      <el-table-column prop="id" label="ID" width="60" sortable="custom" />
      <el-table-column prop="company" sortable="custom" :label="$t('table.companyName')" width="120" />
      <el-table-column prop="name" sortable="custom" :label="$t('table.name')" width="120" />
      <el-table-column prop="position" :label="$t('table.position')" sortable="custom" width="124" />
      <el-table-column prop="department" :label="$t('table.department')" sortable="custom" width="100" />
      <el-table-column prop="email" label="E-mail" width="150" />
      <el-table-column prop="telephone" :label="$t('table.tel')" width="200">
        <template slot-scope="scope">
          <template>
            <p v-for="item of scope.row.telephone">{{ item }}</p>
          </template>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="phone" :label="$t('table.phone')" width="200">
        <template slot-scope="scope">
          <template>
            <p v-for="item of scope.row.phone">{{ item }}</p>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="skype" label="Skype" width="150" />
      <el-table-column prop="wechat" label="weChat" width="150" />
      <el-table-column prop="gmail" label="Gmail" width="150" />
      <el-table-column prop="remark" :label="$t('table.remark')" width="160" /> -->
      <el-table-column prop="updatedAt" width="160" :label="$t('table.editDate')" />
      <el-table-column prop="updatedBy" :label="$t('table.editor')" />
      <el-table-column prop="stateType" :label="$t('table.status')" />
      <el-table-column :label="$t('table.operate')" width="200">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="120px"
        style="width: 400px; margin-left:50px;">

        <el-form-item :label="$t('table.companyName')" prop="company">
          <el-select v-model="form.company" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :remote-method="remoteMethod" :loading="loading" default-first-option>
            <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('table.name')" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item :label="$t('table.position')" prop="position">
          <el-select v-model="form.position" class="filter-item" placeholder="Please select">
            <el-option v-for="item in positionOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('table.department')" prop="department">
          <el-select v-model="form.department" class="filter-item" placeholder="Please select">
            <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="email">
          <el-input v-model="form.email" />
        </el-form-item>

        <el-form-item v-for="(item, i) of form.telephone" :label="[$t('table.tel') + (i + 1)]">
          <el-input v-model="form.telephone[i]" />
          <div v-if="i > 0" class="delete">
            <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem('telephone', i)" />
          </div>
          <div v-if="i === form.telephone.length - 1" class="add">
            <el-button icon="el-icon-circle-plus" type="primary" @click="add('telephone')">{{
              $t('quotation.addRow')
            }}</el-button>
          </div>
        </el-form-item>

        <el-form-item v-for="(item, i) of form.phone" :label="[$t('table.phone') + (i + 1)]">
          <el-input v-model="form.phone[i]" />
          <div v-if="i > 0" class="delete">
            <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem('phone', i)" />
          </div>
          <div v-if="i === form.phone.length - 1" class="add">
            <el-button icon="el-icon-circle-plus" type="primary" @click="add('phone')">{{
              $t('quotation.addRow')
            }}</el-button>
          </div>
        </el-form-item>

        <el-form-item label="Skype">
          <el-input v-model="form.skype" />
        </el-form-item>
        <el-form-item label="wechat">
          <el-input v-model="form.wechat" />
        </el-form-item>
        <el-form-item label="gmail">
          <el-input v-model="form.gmail" />
        </el-form-item>
        <el-form-item :label="$t('table.remark')">
          <el-input v-model="form.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
        <el-form-item :label="$t('table.status')" prop="stateType">
          <el-select v-model="form.stateType" class="filter-item" placeholder="Please select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::contact-person.contact-person/',
      companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      departmentUrl: '/content-manager/collection-types/api::department.department?filters[$and][0][state][$eq]=true',
      positionUrl: '/content-manager/collection-types/api::position.position?filters[$and][0][state][$eq]=true',
      list: [],
      totle: 0,
      listLoading: false,
      loading: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      // 公司
      companyOptions: [],
      // 职位
      positionOptions: [],
      // 部门
      departmentOptions: [],
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      },

      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 部门 可以不填
        // department: [
        //   { required: true, message: '請選擇', trigger: 'blur' }
        // ],
        // 职位
        position: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 狀態
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 獲取公司
    // this.getRelation(this.companyUrl).then((result) => {
    //   this.companyOptions = result
    // });
    // 獲取职位
    this.getRelation(this.positionUrl).then((result) => {
      this.positionOptions = result
    })
    // 獲取部门
    this.getRelation(this.departmentUrl).then((result) => {
      this.departmentOptions = result
    })
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            id: res[index].id,
            // 公司名称
            company: res[index].company ? res[index].company.name : '',
            company_id: res[index].company ? res[index].company.id : '',
            // 姓名
            name: res[index].name,
            // 职位
            position: res[index].position ? res[index].position.name : '',
            // position_id: res[index].position ? [index].position.id : '',
            // 部门
            department: res[index].department ? res[index].department.name : "",
            department_id: res[index].department ? res[index].department.id : "",
            // 电话
            telephone: res[index].telephone,
            telephone1: res[index].telephone ? res[index].telephone[0] : '',
            // 手機
            phone: res[index].phone,
            phone1: res[index].phone ? res[index].phone[0] : '',
            // skype
            skype: res[index].skype,
            // wechat
            wechat: res[index].wechat,
            // gmail
            gmail: res[index].gmail,
            // email
            email: res[index].email,
            // 備註
            remark: res[index].remark,
            // 狀態
            state: res[index].state,
            stateType: res[index].state ? this.$t('table.status1') : this.$t('table.status0'),
            // 修改時間
            updatedAt: timeChange(res[index].updatedAt),
            // 修改人
            updatedBy: res[index].updatedBy ? res[index].updatedBy.firstname : 'null'

          }
          arrList.push(arr)
        }
        this.list = arrList
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    // 排序的方法
    handlesortChange(column) {
      let target = ''
      switch (column.prop) {
        case 'department':
          target = 'department.name'
          break;
        case 'position':
          target = 'position.name'
          break;
        case 'company':
          target = 'company.name'
          break;
        case 'name':
          target = 'name'
          break;
        case 'id':
          target = 'id'
          break;

        default:
          break;
      }
      if (column.order == "ascending") {
        this.listQuery.sort = target + ":ASC"
      }
      if (column.order == "descending") {
        this.listQuery.sort = target + ":DESC"
      }
      this.getList(this.listQuery)
    },
    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字
    async getRelation(url, p) {
      let params = ''
      let rt
      if (p) {
        params = {
          'filters[$and][1][name][$contains]': p,
          'pageSize': 999
        }
      } else {
        params = {
          'pageSize': 999
        }
      }
      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id ? res[index].id : "",
            label: res[index].name ? res[index].name : ""
          }
          arrList.push(arr)
        }
        this.listLoading = false
        rt = arrList
      })
      return rt
    },
    // 點擊修改的事件
    handleClick(row) {
      this.listLoading = true
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      console.log(row)
      const rowForm = {
        id: row.id,
        // 公司名称
        company: row.company,
        company_id: row.company_id,
        // 姓名
        name: row.name,
        // 职位
        position: row.position,
        position_id: row.position_id,
        // 部门
        department: row.department ? row.department : "",
        department_id: row.department_id ? row.department_id : "",
        // 电话
        telephone: row.telephone,
        // 手機
        phone: row.phone,
        // skype
        skype: row.skype,
        // wechat
        wechat: row.wechat,
        // gmail
        gmail: row.gmail,
        // email
        email: row.email,
        // 備註
        remark: row.remark,
        // 狀態
        state: row.state,
        stateType: row.state ? '啟用' : '關閉',
        // 修改時間
        updatedAt: timeChange(row.updatedAt),
        // 修改人
        updatedBy: row.updatedBy.firstname

      }
      this.form = Object.assign({}, rowForm) // copy obj
      console.log(this.form)
    },
    // 確認修改的事件
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const temp = {
            id: this.form.id,
            name: this.form.name ? this.form.name : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            company: relationReturn(this.form.company.value, this.form.company_id),
            position: relationReturn(this.form.position.value, this.form.position_id),
            department: relationReturn(this.form.department.value, this.form.position_id),
            // 數組格式需判斷是否有內容，沒有則用[""]標識
            telephone: this.form.telephone.length ? this.form.telephone : [''],
            phone: this.form.phone.length ? this.form.phone : [''],
            skype: this.form.skype ? this.form.skype : '',
            wechat: this.form.wechat ? this.form.wechat : '',
            gmail: this.form.gmail ? this.form.gmail : '',
            email: this.form.email ? this.form.email : '',
            skype: this.form.skype ? this.form.skype : '',
            remark: this.form.remark ? this.form.remark : '',
            state: this.form.stateType.value != '0'

          }

          const tempData = Object.assign({}, temp)
          console.log(tempData.telephone)
          postData(this.baseUrl, 'put', temp.id, tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 清空提交表
    resetForm() {
      this.form = {
        id: '',
        // 公司名称
        company: '',
        company_id: '',
        // 姓名
        name: '',
        // 职位
        position: '',
        position_id: '',
        // 部门
        department: '',
        department_id: '',
        // 电话
        telephone: [''],
        telephone1: '',
        // 手機
        phone: [''],
        phone1: '',
        // skype
        skype: '',
        // wechat
        wechat: '',
        // gmail
        gmail: '',
        // email
        email: '',
        // 備註
        remark: '',
        // 狀態
        state: '',
        stateType: ''
      }
    },
    // 创建的按钮打开
    handleCreate() {
      this.resetForm()
      this.form.stateType = '啟用'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    // 提交創建的事件
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          const temp = {
            name: this.form.name ? this.form.name : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            company: relationCreateReturn(this.form.company.value),
            position: relationCreateReturn(this.form.position.value),
            department: relationCreateReturn(this.form.department.value),
            // 數組格式需判斷是否有內容，沒有則用[""]標識
            telephone: this.form.telephone.length ? this.form.telephone : [''],
            phone: this.form.phone.length ? this.form.phone : [''],
            skype: this.form.skype ? this.form.skype : '',
            wechat: this.form.wechat ? this.form.wechat : '',
            gmail: this.form.gmail ? this.form.gmail : '',
            email: this.form.email ? this.form.email : '',
            skype: this.form.skype ? this.form.skype : '',
            remark: this.form.remark ? this.form.remark : '',
            state: this.form.stateType.value != '0'

          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'post', '', tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.baseUrl, 'delete', row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      }
      this.getList(this.listQuery)
    },
    changeCountry(item) {
      this.form.currency = item.currency
    },
    // 新增或者修改时，新增电话或者手机的方法
    add(t) {
      if (t === 'telephone') {
        this.form.telephone.push('')
      } else if (t === 'phone') {
        this.form.phone.push('')
      }
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(t, delete_index) {
      if (t === 'telephone') {
        this.form.telephone.splice(delete_index, 1)
      } else if (t === 'phone') {
        this.form.phone.splice(delete_index, 1)
      }
    },
    // 遠程搜索
    remoteMethod(query) {
      if (query !== '') {
        console.log('111')
        // 獲取公司
        this.getRelation(this.companyUrl, query).then((result) => {
          this.companyOptions = result
        })
        console.log(this.companyOptions)
      } else {
        this.getRelation(this.companyUrl).then((result) => {
          this.companyOptions = result
        })
      }
    },

    // 導出Excel
    handleDownload() {
      this.$confirm(this.$t('table.goonPrompt'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['ID', this.$t('table.companyName'), this.$t('table.name'), this.$t('table.position'), this.$t('table.department'), 'E-mail', this.$t('table.tel'), this.$t('table.phone'), 'Skype', 'weChat', 'Gmail', this.$t('table.remark'), this.$t('table.editDate'), this.$t('table.editor'), this.$t('table.status')]
          const filterVal = ['id', 'company', 'name', 'position', 'department', 'email', 'telephone', 'phone', 'skype', 'wechat', 'gmail', 'remark', 'updatedAt', 'updatedBy', 'stateType']
          const data = this.formatJson(filterVal, this.list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.$t('route.contacts'),
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.cancelPrompt')
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },


  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}
</style>
