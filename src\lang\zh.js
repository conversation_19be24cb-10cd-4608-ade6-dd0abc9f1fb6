export default {
  route: {
    dashboard: '控制台',
    notice: '通告列表',
    mainbanner: '首頁海報',
    busroute: '巴士路線',
    community: '社區',
    foodtype: '美食匯',
    foodtypeDetail: '美食匯詳情',
    foodtypeImg1: '菜單圖片',
    foodtypeImg2: '菜品圖片',
    machine: '設備',
    clubhouse: 'Clubhouse',
    clubhousePoster: 'Clubhouse-Poster',
    versionManagement: '版本管理',
    en:"(Eng)",
    tc:"(繁)",
    zh:"(簡)",
    novoland:"Novoland",
    clubhouse:"Clubhouse",


    // list
    order:"順序",
    name:"名稱",
    name_en:"名稱(Eng)",
    name_tc:"名稱(繁)",
    name_zh:"名稱(简)",
    desc_en:"簡介(Eng)",
    desc_tc:"簡介(繁)",
    desc_zh:"簡介(简)",
    view:"查看",
    image:"圖片",
    document:"文件",
    format:"格式",
    resolution:"分辨率",
    size:"大小",
    noticeVisible:"通告可見設備",
    noticeVisibleSelect:"请選擇（不選為全可見）",

    startDate:"開始日期",
    endDate:"結束日期",
    showDate:"展示時間",


    active:"生效中",
    noactive:"未生效",


    route:"線路",
    stop:"站點",
    number:"號碼",

    apk:"Apk",
    version:"版本號",
    versionDetail:"更新內容",
    apkNotice:"請注意：版本號必須要比當前版本號更高，否則無法觸發更新",

    location:"位置",


    // 上传
    select:"選擇文件",
    upload:"上傳",
    success: '成功',

    database: '數據庫管理',
    ProductCategory: '產品分類',
    PaymentMethod: '付款方式',
    Supply: '貨源列表',
    CompanyClassification: '公司分類',
    Country: '國家',
    Department: '部門',
    Position: '職位',
    Clause: '條文',
    Currency: '貨幣',

    statistical: '統計分析',
    turnover: '流水統計',
    schedule: '進度表',

  },
  login: {
    title: '系统登录',
    logIn: '登录',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！'
  },
  table: {
    title: '标题',
    importance: '重要性',
    type: '类型',
    search: '搜索',
    reset: '重置',
    add: '新增',
    query:'搜索名称或備註',
    queryCompany:'搜索公司名稱',
    queryContacts:'搜索聯繫人',
    queryCurrency:'搜索貨幣',
    querySupply:'搜索貨源',
    queryProductCategory:'搜索分類',

    allSelect:'全部',
    inv1:'已開發票',
    inv2:'未開發票',
    dn1:'已開送貨單',
    dn2:'未開送貨單',

    company:'公司',
    contact:'聯繫人',
    project:'項目名稱',
    quote:'報價單號',
    quoteDate:'報價時間',
    createDate:'創建日期',
    currency:'貨幣',
    total:'總價',
    inv:'發票',
    invDetail:'查看發票',
    invNo:'暫無發票',
    dn:'送貨單',
    editDate:'最後編輯時間',
    editor:'編輯人',
    remark:'備註',
    status:'狀態',
    operate:'操作',


    productName:'項目名稱',
    category:'分類',
    unit:'單位',
    spec:'規格',
    desc:'描述',
    supply:'貨源',
    cost:'成本',
    total:'售價',

    companyName:'公司名稱',
    country:'國家',
    address:'地址',
    tel:'電話',
    website:'網址',

    name:'姓名',
    department:'部門',
    position:'職位',
    phone:'手機',

    name2:'名稱',
    language:'語言',
    clause:'條文',


    enterTip:'請輸入',
    selectTip:'請選擇',

    export: '导出',
    prompt:'提示',
    status: '状态',
    status0: '關閉',
    status1: '啟用',
    actions: '操作',
    create:'新增',
    edit: '编辑',
    detail: '詳情',
    delete: '删除',
    deleteSuccess: '刪除成功',
    deleteCancel: '已取消刪除',
    cancelPrompt: '已取消',
    deleteText: '这确定删除吗？',
    goonPrompt: '是否繼續？',
    cancel: '取 消',
    confirm: '确 定'
  },
  quotation:{
    title:'報價單的基礎信息',
    billTitle:'發票與送貨單',
    project:'項目名稱',
    quoteDate:'報價時間',
    company:'公司名稱',
    companyAddress:'公司地址',
    currency:'貨幣',
    contact:'聯繫人',
    payment:'付款方式',
    paymentRemark:'付款方式備註',


    productTitle:'報價單產品內容',
    index:'序號',
    product:'產品名稱',
    ProductCategory: '產品分類',
    number: '數量',
    unitPrice: '單價',
    allPrice: '合計',
    cost: '成本',
    delete: '移除',
    addRow:'增加一行',
    total:'總數',
    totalPrice:'總價',


    clauseTitle:'條文內容',
    clauseCategory: '條文類型',
    clauseDetail: '條文詳情',

    add:'新增',
    save:'儲存',
    downloadPdf:'導出PDF',

    bill:{
      name:'發票',
      add:'開具發票',
      edit:'重開發票',
      view:'查看發票',
      text:'暫無發票',
    },
    delivery:{
      name:'送貨單',
      add:'開具送貨單',
      edit:'修改送貨單',
      recipient:'收貨人',
      recipientTel:'收貨電話',
      recipientAddress:'收貨地址',
    },
    otherName1:'基礎信息',
    otherName2:'產品內容',
  },
}
