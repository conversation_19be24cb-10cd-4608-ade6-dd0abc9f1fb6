import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

// export function getInfo(token) {
//   return request({
//     url: '/vue-element-admin/user/info',
//     method: 'get',
//     params: { token }
//   })
// }

export function getInfo(token) {
  return new Promise(async (resolve, reject) => {
    let role
    let roleArr = []
    await getUser().then((response) => {
      console.log(response.data);
      role = response.data.username
      roleArr.push(role)
    });
    console.log(roleArr);
    await resolve({
      code: 200,
      data: {
        roles: roleArr,
        introduction: "I am CRM",
        avatar:
          "https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif",
        name: "Super Admin",
      },
    });
    reject({ code: 500, msg: "network error" });
  });
}

// 獲取产品列表
const baseUrlProduct = "/admin/users/me";
export function getUser(params) {
  return request({
    url: baseUrlProduct,
    method: "get",
    params,
  });
}
// export function getInfo(token) {
//   return new Promise((resolve, reject) => {
//     resolve({
//         "code": 200,
//         "data": {
//           "roles": [
//             'admin'
//           ],
//           "introduction": "I am CRM",
//           "avatar": "https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif",
//           "name": "Super Admin"
//         }
//       });
//       reject({ "code": 500, msg: 'network error' })
//     })
//   }

  // export function logout() {
  //   return request({
  //     url: '/vue-element-admin/user/logout',
  //     method: 'post'
  //   })
  // }
  export function logout() {
    return new Promise((resolve, reject) => {
      resolve({
        code: 20000,
        data: 'success'
      });
        reject({ "code": 500, msg: 'network error' })
      })
    }
