<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1>公司信息收集列表</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="image" label="圖片" width="140">
        <template slot-scope="{row}">
          <a :href='baseURL + row.image.url' target="_blank">
            <img class="avatar" :src="baseURL + row.image.formats.thumbnail.url" alt="">
          </a>
        </template>
      </el-table-column> -->
      <el-table-column prop="name" label="公司名稱" width="" />
      <el-table-column prop="url" label="公司網址" width="" />
      <el-table-column prop="mail_address" label="郵箱" width="" />
      <!-- <el-table-column prop="content" label="更新內容">
        <template slot-scope="{row}">
          <p style="white-space: pre-wrap;">{{ row.content }}</p>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
            }}</el-button>
          <el-button type="success" icon="el-icon-magic-stick" size="small" @click="handleGenerateEmail(scope.row)">
            AI生成郵件
          </el-button>
          <!-- <el-button type="primary" size="small">
            <a :href='baseURL + scope.row.file.url' target="_blank">查看PDF</a>
          </el-button> -->
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
              }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <!-- 第一个弹框：输入基本信息 -->
    <el-dialog title="輸入公司信息" :visible.sync="dialogInputVisible">
      <el-form ref="inputForm" :model="inputForm" label-position="right" :rules="inputRules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-alert title="溫馨提示" type="info"
          description="系統將根據公司名稱或官網地址獲取相關信息。請至少填寫其中一項，建議填寫官網地址以獲得更準確的結果，因為相同公司名稱可能存在多個。" :closable="false" show-icon
          style="margin-bottom: 20px;" />

        <el-form-item label="公司名稱" prop="name">
          <el-input v-model="inputForm.name" />
          <span class="el-form-item__description">若有多個相同公司名稱，可能影響準確性</span>
        </el-form-item>

        <el-form-item label="公司官網地址" prop="url">
          <el-input v-model="inputForm.url" />
          <span class="el-form-item__description">填寫官網地址可提供最準確的公司信息</span>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogInputVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="fetchCompanyDetails">
          下一步
        </el-button>
      </div>
    </el-dialog>

    <!-- 第二个弹框：显示详细信息 -->
    <el-dialog :title="dialogStatus === 'create' ? '確認公司信息' : '編輯公司信息'" :visible.sync="dialogDetailVisible">
      <el-form ref="form" :model="form" label-position="right" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-form-item label="公司名稱">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item label="公司官網地址">
          <el-input v-model="form.url" />
        </el-form-item>

        <el-form-item label="聯繫人">
          <el-input v-model="form.contact_name" />
        </el-form-item>

        <el-form-item label="聯繫人關係">
          <el-input v-model="form.contact_relationship" />
        </el-form-item>

        <el-form-item label="語言">
          <el-input v-model="form.lang" />
        </el-form-item>

        <el-form-item label="郵箱地址">
          <el-input v-model="form.mail_address" />
        </el-form-item>

        <el-form-item label="電話">
          <el-input v-model="form.telephone" />
        </el-form-item>

        <el-form-item label="網站內容">
          <el-input type="textarea" v-model="form.web_conttext" :rows="4" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogDetailVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="createData">
          {{ dialogStatus === 'create' ? '確認提交' : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 在 fetchCompanyDetails 过程中显示进度 -->
    <el-dialog title="任務處理中" :visible.sync="progressDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false" width="500px" class="progress-dialog">
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
        </div>

        <div class="status-text">{{ currentStatusText }}</div>

        <div class="terminal-window">
          <div class="terminal-header">
            <div class="terminal-button red"></div>
            <div class="terminal-button yellow"></div>
            <div class="terminal-button green"></div>
          </div>
          <div class="terminal-body" ref="terminalBody">
            <div v-for="(log, index) in progressLogs" :key="index" class="log-item"
              :class="{ 'active': index === activeLogIndex }">
              <div class="log-time">{{ log.time }}</div>
              <div class="log-status">{{ log.status }}</div>
              <div class="log-description">{{ log.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加 AI 生成邮件的弹框 -->
    <el-dialog title="AI生成郵件內容" :visible.sync="generateDialogVisible" width="800px" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div v-loading="generating">
        <el-form ref="generateForm" :model="generateForm" label-width="120px">
          <el-form-item label="選擇模板">
            <el-select v-model="generateForm.templateId" placeholder="請選擇模板" style="width: 100%">
              <el-option v-for="item in templates" :key="item.id" :label="item.name" :value="item.id">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.description }}</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="模板內容">
            <el-input type="textarea" :rows="4" placeholder="模板內容" v-model="generateForm.templateText">
            </el-input>
          </el-form-item>

          <el-form-item label="網站內容">
            <el-input type="textarea" :rows="4" placeholder="網站內容" v-model="generateForm.webContent">
            </el-input>
          </el-form-item>

          <el-form-item label="生成結果">
            <div class="result-container">
              <Typewriter v-if="showTypewriter && !isEditing" :text="generateForm.generatedText" :speed="8"
                :enable-typewriter="typewriterIng" @typing-completed="onTypingCompleted" />
              <div v-else class="edit-result-container">
                <el-input type="textarea" :rows="6" v-model="generateForm.generatedText" :readonly="!isEditing"
                  :placeholder="isEditing ? '請輸入郵件內容' : '生成的郵件內容'">
                </el-input>
              </div>

              <el-button type="primary" size="small" class="edit-button" @click="toggleEdit" v-if="hasGenerated">
                {{ isEditing ? '完成' : '編輯' }}
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">
          取 消
        </el-button>
        <el-button type="primary" @click="handleGenerate" :loading="generating">
          {{ hasGenerated ? '重新生成' : '生成' }}
        </el-button>
        <el-button type="success" @click="handleSendEmail" :disabled="!generateForm.generatedText || isTyping">
          使用此內容發送郵件
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import axios from 'axios'
import { getData, postData, uploadData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'
import { uploadService } from '@/utils/request'
import FileUpload from '@/components/Upload'
import OpenCC from 'opencc-js'
import Typewriter from '@/components/Typewriter'

export default {
  name: 'Product',
  components: { Pagination, FileUpload, Typewriter },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      // baseURL: 'https://yoho.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: "getmail",
      listLoading: false,
      uploadLoading: false,
      btnLoading: false,
      file: "",
      fileName: "",
      fileId: "",
      fileUploadType: false,
      // 弹框
      dialogFormVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {
        name: "",
        url: "",
        contact_name: "",
        contact_relationship: "",
        lang: "",
        mail_address: "",
        telephone: "",
        web_conttext: ""
      },
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },

      // 表单验证规则
      rules: {

      },
      typewriterIng: true,
      uploadProgress: 0,
      uploading: false,
      uploadUrl: 'http://localhost:3000',
      progressStatus: '',
      baseUrl: process.env.VUE_APP_BASE_API, // 从环境变量中获取
      uploadApi: process.env.VUE_APP_UPLOAD_API,
      dialogInputVisible: false, // 第一个弹框
      dialogDetailVisible: false, // 第二个弹框
      inputForm: {
        name: '',
        url: ''
      },
      inputRules: {
        name: [{ required: false, message: '請輸入公司名稱', trigger: 'blur' }],
        url: [{ required: false, message: '請輸入公司官網地址', trigger: 'blur' }]
      },
      progressDialogVisible: false,
      progressPercentage: 0,
      progressLogs: [],
      activeLogIndex: -1,
      progressSteps: [
        {
          status: '任務已排隊 (Task Queued)',
          description: '系統已接收處理請求，任務正在等候序列中，待分配運算資源。'
        },
        {
          status: '初始化分析 (Initializing Analysis)',
          description: '系統開始處理任務，進行初步的輸入驗證與環境設定。'
        },
        {
          status: '目標網域識別 (Identifying Target Domain)',
          description: '若僅提供企業名稱，系統正在透過搜尋引擎或數據庫比對，精準定位目標企業的官方網站URL。若已提供URL，則進行初步可訪問性驗證。'
        },
        {
          status: '建立安全連線 (Establishing Secure Connection)',
          description: '系統嘗試與目標伺服器建立穩定且安全的網路連線通道（例如HTTPS）。'
        },
        {
          status: '深度內容擷取 (Acquiring Deep Content)',
          description: '系統正在智慧導航網站結構（例如追蹤"關於我們"、"聯繫方式"頁面），下載相關頁面的原始碼與資源。'
        },
        {
          status: '網頁結構解析 (Parsing Web Structure)',
          description: '系統正在解析下載的HTML/DOM結構，理解頁面佈局與元素關係，為資訊提取做準備。'
        },
        {
          status: '關鍵資訊掃描 (Scanning for Key Information)',
          description: '利用自然語言處理(NLP)與模式識別技術，在網頁文本中初步掃描潛在的聯絡人姓名、職位、郵件地址、電話號碼等關鍵字詞。'
        },
        {
          status: '數據精準提取 (Extracting Precise Data)',
          description: '系統正在應用正規表示式(Regex)及特定演算法，從已識別的區域中精確提取結構化數據，如驗證郵件格式、電話號碼格式等。'
        },
        {
          status: '資訊整合與驗證 (Consolidating & Validating Information)',
          description: '將來自不同頁面或區塊的提取碎片資訊進行彙總、去重、交叉驗證，並判定主要聯絡方式與語言偏好。'
        },
        {
          status: '核心數據提取完成 (Core Data Extraction Complete)',
          description: '企業基本資料、聯絡方式等核心資訊已成功提取並結構化存儲，準備進入後續流程（如郵件個性化或標記為完成）。'
        }
      ],
      currentStatusText: '正在處理您的請求...',
      // 添加 OpenCC 转换器实例
      converter: null,
      generateDialogVisible: false,
      generating: false,
      templates: [],
      generateForm: {
        templateId: '',
        templateText: '',
        webContent: '',
        generatedText: '',
        lang: '',
        companyName: ''
      },
      showTypewriter: false,
      isTyping: false,
      hasGenerated: false,
      typingCompleted: false,
      isEditing: false
    }
  },
  created() {
    // 初始化转换器
    this.initConverter();
    this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list);
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    /*
    上傳文件前的处理
    */
    handleBeforeUpload(file) {
      const isAPK = file.type === 'application/vnd.android.package-archive';
      if (!isAPK) {
        this.$message.error('只能上傳 APK 文件！');
        return false;
      }
      return true;
    },
    customUpload({ file }) {
      this.uploading = true;
      this.uploadProgress = 0;
      this.fileUploadType = false;
      this.file = file;
      this.fileName = file.name;

      const formData = new FormData();
      formData.append('file', file);

      return uploadService({
        url: '/upload',
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          this.uploadProgress = progress;
        }
      })
        .then(response => {
          this.uploading = false;
          this.fileUploadType = true;

          this.form.url = response.data.url;

          this.$notify({
            title: '上傳成功',
            message: `文件 ${this.fileName} 已上傳到服務器`,
            type: 'success'
          });

          return response.data;
        })
        .catch(error => {
          this.handleUploadError(error);
          throw error;
        });
    },
    handleUploadError(error) {
      this.uploading = false;
      this.uploadProgress = 0;
      this.fileUploadType = false;

      this.$notify({
        title: '上傳失敗',
        message: error.message || `文件：${this.fileName} 上傳失敗`,
        type: 'error'
      });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    /*
    點擊修改的事件
    */
    handleClick(row) {
      this.resetForm();
      this.dialogStatus = 'update';
      this.dialogDetailVisible = true;
      this.formRow = {
        id: row.id,
        name: row.name,
        url: row.url,
        contact_name: row.contact_name,
        contact_relationship: row.contact_relationship,
        lang: row.lang,
        mail_address: row.mail_address,
        telephone: row.telephone,
        web_conttext: row.web_conttext
      };

      this.form = Object.assign({}, this.formRow);

      // 修改确认按钮文本
      this.$nextTick(() => {
        // 可以在此处添加任何需要在DOM更新后执行的代码
      });
    },
    /*
    清空提交表
    */
    resetForm() {
      this.inputForm = {
        name: '',
        url: ''
      };
      this.form = {
        name: "",
        url: "",
        contact_name: "",
        contact_relationship: "",
        lang: "",
        mail_address: "",
        telephone: "",
        web_conttext: ""
      };
      // 文件上傳部分的清零
      this.file = "";
      this.fileId = "";
      this.fileName = "";
      this.fileUploadType = false;
      this.uploadProgress = 0;
      this.uploading = false;
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogInputVisible = true
      this.$nextTick(() => {
        this.$refs['inputForm']?.clearValidate()
      })
    },

    /*
    获取公司详细信息
    */
    async fetchCompanyDetails() {
      this.$refs['inputForm'].validate(async (valid) => {
        if (valid) {
          if (!this.inputForm.name && !this.inputForm.url) {
            this.$notify({
              title: 'Warning',
              message: '公司名稱和官網地址不能同時為空',
              type: 'warning'
            });
            return;
          }

          try {
            // 显示进度对话框
            this.startProgressAnimation();

            // 根据是否有 URL 来确定处理时间
            const isUrlMode = !!this.inputForm.url;
            const maxDuration = isUrlMode ? 20000 : 60000; // URL模式20秒，Name模式60秒

            // 获取公司信息
            let companyInfo;

            // 创建一个Promise，用于获取公司信息
            const fetchDataPromise = new Promise(async (resolve) => {
              if (this.inputForm.url) {
                companyInfo = await this.fetchCompanyInfo(
                  '',
                  this.inputForm.url,
                  "7491133500642590761"
                );
              } else if (this.inputForm.name) {
                companyInfo = await this.fetchCompanyInfo(
                  this.inputForm.name,
                  '',
                  "7491266478631026715"
                );
              }
              resolve(companyInfo);
            });

            // 同时运行动画和数据获取
            const [_, info] = await Promise.all([
              this.runProgressAnimation(maxDuration),
              fetchDataPromise
            ]);

            // 将所有中文内容转换为繁体
            const convertedInfo = this.convertToTraditional(info);

            // 更新表单数据
            this.form = {
              ...convertedInfo,
              name: this.inputForm.name || convertedInfo.name,
              url: this.inputForm.url || convertedInfo.url
            };

            // 关闭进度对话框，打开详情对话框
            this.progressDialogVisible = false;
            this.dialogInputVisible = false;
            this.dialogDetailVisible = true;
          } catch (error) {
            this.progressDialogVisible = false;
            this.$notify({
              title: 'Error',
              message: error.message || '獲取公司信息失敗',
              type: 'error'
            });
          } finally {
            this.btnLoading = false;
          }
        }
      });
    },

    // 添加初始化转换器方法
    initConverter() {
      this.converter = OpenCC.Converter({ from: 'cn', to: 'hk' });
    },

    // 修改繁体转换方法
    convertToTraditional(obj) {
      if (!this.converter) {
        return obj;
      }

      const convert = (value) => {
        if (typeof value === 'string') {
          return this.converter(value);
        }
        return value;
      };

      if (typeof obj === 'object' && obj !== null) {
        const result = Array.isArray(obj) ? [] : {};
        for (const key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            result[key] = this.convertToTraditional(obj[key]);
          }
        }
        return result;
      }

      return convert(obj);
    },

    /*
    修改创建数据方法
    */
    async fetchCompanyInfo(name, url, workflowId) {
      try {
        const response = await axios({
          method: 'post',
          url: 'https://api.coze.cn/v1/workflow/run',
          headers: {
            'Authorization': 'Bearer pat_dIitRVSaXQ4BisRZtC8NGPM7Mpckxt7Mge5bkceYVXVgGhZ7XSriL9BIUp6ugHYE',
            'Content-Type': 'application/json'
          },
          data: {
            parameters: {
              name: name,
              url: url
            },
            is_async: false,
            workflow_id: workflowId
          }
        });

        if (response.data.code === 0) {
          const data = JSON.parse(response.data.data);
          const companyInfo = JSON.parse(data.data);
          return companyInfo;
        }
        throw new Error(response.data.msg || '請求失敗');
      } catch (error) {
        throw error.response?.data?.msg || error.message || '請求失敗';
      }
    },

    /*
    修改创建数据方法
    */
    async createData() {
      try {
        this.btnLoading = true;

        if (this.dialogStatus === 'create') {
          // 新增模式 - 使用 POST
          await postData(this.apiName, 'post', '', this.form);

          this.$notify({
            title: 'Success',
            message: 'Create Successfully',
            type: 'success',
            duration: 1000
          });
        } else {
          // 编辑模式 - 使用 PUT
          const tempData = Object.assign({}, this.form);
          await postData(this.apiName, 'put', tempData.id, tempData);

          this.$notify({
            title: 'Success',
            message: 'Update Successfully',
            type: 'success',
            duration: 1000
          });
        }

        this.getList(this.listQuery);
        this.dialogDetailVisible = false;
      } catch (error) {
        this.$notify({
          title: 'Error',
          message: error.message || (this.dialogStatus === 'create' ? 'Create Failed' : 'Update Failed'),
          type: 'error',
          duration: 1000
        });
      } finally {
        this.btnLoading = false;
      }
    },

    /*
    管理關聯表表的方法
    */
    connectFunc(newId, oldId) {
      if (newId) {
        if (oldId) {
          if (oldId == newId) {
            return {
              "disconnect": [],
              "connect": []
            }
          } else {

            return {
              "disconnect": [
                {
                  "id": parseInt(oldId)
                }],
              "connect": [
                {
                  "id": parseInt(newId),
                  "position": {
                    "end": true
                  }
                }
              ]
            }
          }

        } else {
          return {
            "disconnect": [],
            "connect": [
              {
                "id": parseInt(newId),
                "position": {
                  "end": true
                }
              }
            ]
          }
        }

      } else if (oldId) {
        return {
          "disconnect": [
            {
              "id": parseInt(oldId),
              "position": {
                "end": true
              }
            }],
          "connect": []
        }
      }
      else {
        return {
          "disconnect": [],
          "connect": []
        }
      }


    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.apiName, "DELETE", row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    handleUploadSuccess(url) {
      this.form.url = url;
      this.fileUploadType = true;
    },

    handleUploadError(error) {
      this.fileUploadType = false;
      console.error('Upload error:', error);
    },

    // 开始进度动画
    startProgressAnimation() {
      this.progressDialogVisible = true;
      this.progressPercentage = 0;
      this.progressLogs = [];
      this.activeLogIndex = -1;
    },

    // 运行进度动画
    async runProgressAnimation(duration) {
      const totalSteps = this.progressSteps.length;
      const stepDuration = duration / totalSteps;

      for (let i = 0; i < totalSteps; i++) {
        // 添加日志
        const now = new Date();
        const timeString = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

        this.progressLogs.push({
          time: timeString,
          status: this.progressSteps[i].status,
          description: this.progressSteps[i].description
        });

        // 设置当前活动日志
        this.activeLogIndex = this.progressLogs.length - 1;

        // 更新进度百分比
        this.progressPercentage = ((i + 1) / totalSteps) * 100;

        // 更新状态文本
        this.currentStatusText = this.progressSteps[i].status;

        // 滚动到底部
        await this.$nextTick();
        if (this.$refs.terminalBody) {
          this.$refs.terminalBody.scrollTop = this.$refs.terminalBody.scrollHeight;
        }

        // 等待该步骤的持续时间
        await new Promise(resolve => setTimeout(resolve, stepDuration));
      }

      // 完成所有步骤
      this.progressPercentage = 100;
      this.currentStatusText = '處理完成，正在加載結果...';
    },

    // 处理 AI 生成按钮点击
    async handleGenerateEmail(row) {
      this.generateForm = {
        templateId: '',
        templateText: '',
        webContent: row.web_conttext || '',
        generatedText: '',
        lang: row.lang || '繁體中文',
        companyName: row.name || ''
      };

      try {
        // 获取模板列表
        const response = await getData('template', '');
        this.templates = response.results;

        this.generateDialogVisible = true;
      } catch (error) {
        this.$notify({
          title: 'Error',
          message: '獲取模板列表失敗',
          type: 'error'
        });
      }
    },

    // 监听模板选择变化
    async handleTemplateChange(templateId) {
      const template = this.templates.find(t => t.id === templateId);
      if (template) {
        this.generateForm.templateText = template.text;
        this.hasGenerated = false;
        this.showTypewriter = false;
        this.typingCompleted = false;
        this.generateForm.generatedText = '';
      }
    },

    // 生成邮件内容
    async handleGenerate() {
      if (!this.generateForm.templateId) {
        this.$message.warning('請選擇一個模板');
        return;
      }

      this.generating = true;
      this.typingCompleted = false;
      this.isTyping = true;
      this.typewriterIng = true;
      try {

        // 构建翻译指令
        const systemInstruction = {
          parts: [{
            text: `你是一个专业的邮件内容编辑师，请结合模板内容以及收件人公司信息
帮我组合成适合该公司的产品营销信息，请使用${this.generateForm.lang}。請直接輸出郵件正文，不要跟我對話`
          }]
        };

        // 构建请求内容
        const requestData = {
          system_instruction: systemInstruction,
          contents: [{
            parts: [{
              text: `模板内容:${this.generateForm.templateText};;
收件人公司信息:${this.generateForm.webContent}`
            }]
          }]
        };

        // 调用Gemini API
        const response = await axios.post(
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?alt=sse&key=AIzaSyDKSuAGKwo_wlt-Q-LcSTY7rJO8ysMh6Ho',
          requestData,
          {
            headers: {
              'Content-Type': 'application/json'
            },
            responseType: 'text'
          }
        );

        // 解析SSE格式的响应
        const lines = response.data.split('\n');
        let translatedText = '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonData = JSON.parse(line.substring(6));
              if (jsonData.candidates && jsonData.candidates[0] && jsonData.candidates[0].content) {
                translatedText = jsonData.candidates[0].content.parts[0].text;
                break;
              }
            } catch (e) {
              console.log('解析行失敗:', line);
            }
          }
        }

        if (translatedText) {
          this.generateForm.generatedText = translatedText;
          this.showTypewriter = true;
          this.hasGenerated = true;

          this.$notify({
            title: 'Success',
            message: '郵件內容生成成功',
            type: 'success'
          });
        } else {
          throw new Error('未能獲取有效的生成內容');
        }
      } catch (error) {
        this.$notify({
          title: 'Error',
          message: error.message || '生成郵件內容失敗',
          type: 'error'
        });
      } finally {
        this.generating = false;
      }
    },

    // 打字完成的回调
    onTypingCompleted() {
      this.isTyping = false;
      this.typingCompleted = true;
    },

    // 发送邮件方法
    handleSendEmail() {
      this.$notify({
        title: '功能開發中',
        message: '郵件發送功能開發中，敬請期待',
        type: 'info',
        duration: 3000
      });
    },

    // 修改关闭弹框时的重置方法
    handleClose() {
      this.showTypewriter = false;
      this.isTyping = false;
      this.typingCompleted = false;
      this.hasGenerated = false;
      this.generateDialogVisible = false;
      this.generateForm.generatedText = '';
      this.isEditing = false;
    },

    // 修改切换编辑状态的方法
    toggleEdit() {
      this.isEditing = !this.isEditing;
      // 确保不会重新触发打字机效果
      this.typewriterIng = false;
    }
  },

  watch: {
    'generateForm.templateId': {
      handler: 'handleTemplateChange',
      immediate: false
    },
    generateDialogVisible(val) {
      if (!val) {
        this.handleClose();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}


.upload-demo {
  margin-bottom: 15px;
}

.el-progress {
  margin-top: 10px;
  margin-bottom: 10px;
}

.fileName {
  margin-left: 10px;
  color: #606266;
}

.el-form-item__description {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
  display: block;
}

.progress-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e5e9f2;
  border-radius: 3px;
  margin-bottom: 20px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #36d1dc, #5b86e5);
  transition: width 0.3s ease;
  border-radius: 3px;
}

.status-text {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 15px;
  text-align: center;
}

.terminal-window {
  width: 100%;
  height: 350px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  background-color: #1e1e1e;
  display: flex;
  flex-direction: column;
}

.terminal-header {
  height: 30px;
  background-color: #323232;
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.terminal-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.terminal-button.red {
  background-color: #ff5f56;
}

.terminal-button.yellow {
  background-color: #ffbd2e;
}

.terminal-button.green {
  background-color: #27c93f;
}

.terminal-body {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  font-family: "Monaco", "Consolas", monospace;
}

.log-item {
  margin-bottom: 10px;
  padding: 5px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
}

.log-item.active {
  background-color: rgba(79, 192, 141, 0.1);
  border-left: 3px solid #4fc08d;
}

.log-time {
  color: #888;
  font-size: 12px;
  margin-bottom: 2px;
}

.log-status {
  color: #4fc08d;
  font-weight: bold;
  margin-bottom: 4px;
}

.log-description {
  color: #bbb;
  font-size: 13px;
  line-height: 1.5;
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.active .log-status {
  animation: blink 1.5s infinite;
  color: #5b86e5;
}

.generate-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.el-textarea.is-disabled .el-textarea__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}

.template-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.result-container {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  min-height: 150px;
  border: 1px solid #dcdfe6;

  .el-textarea {
    .el-textarea__inner {
      background-color: transparent;
      border: none;
      padding: 0;

      &:focus {
        border: 1px solid #409EFF;
        padding: 5px;
      }
    }
  }
}

.typewriter-container {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #2c3e50;
}

.edit-result-container {
  position: relative;

  .edit-button {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1;
  }

  .el-textarea {
    .el-textarea__inner {
      background-color: transparent;
      border: none;
      padding: 0;

      &:focus {
        border: 1px solid #409EFF;
        padding: 5px;
      }
    }
  }
}
</style>
