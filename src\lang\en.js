export default {
  route: {
    dashboard: 'Control panel',
    notice: 'Notice',
    mainbanner: 'MainBanner',
    busroute: 'BusRoute',
    community: 'Community',
    foodtype: 'Food',
    foodtypeDetail: 'Detail',
    foodtypeImg1: 'Menu pictures',
    foodtypeImg2: 'Dish pictures',
    machine: 'Machine',
    clubhouse: 'Clubhouse',
    clubhousePoster: 'Clubhouse-Poster',
    versionManagement: 'Version',
    en:"(Eng)",
    tc:"(繁)",
    zh:"(簡)",
    novoland:"Novoland",
    clubhouse:"Clubhouse",


    // list
    order:"Order",
    name:" Name",
    name_en:"Name(Eng)",
    name_tc:"Name(繁)",
    name_zh:"Name(简)",
    desc_en:"Desc(Eng)",
    desc_tc:"Desc(繁)",
    desc_zh:"Desc(简)",
    view:"View",
    image:"Picture",
    document:"Document",
    format:"Format",
    resolution:"Resolution",
    size:"Size",
    noticeVisible:"Notice visible device",
    noticeVisibleSelect:"Please select (not selected for all visible)",


    startDate:"Start Date",
    endDate:"End Date",
    showDate:"Show Date",


    active:"Effect",
    noactive:"Not Active",

    route:"Route",
    stop:"Stop",
    number:" Number",

    apk:"Apk",
    version:"Version serial number",
    versionDetail:"Update detail",
    apkNotice:"Please note: the version number must be higher than the current version number, otherwise the update cannot be triggered",

    // 上传
    select:"Select a document",
    upload:"Upload",
    success: 'Success',

    location:" location",




    database: 'Database',
    ProductCategory: 'Products Category',
    PaymentMethod: 'Payment Method',
    Supply: 'Products source',
    CompanyClassification: 'Company category',
    Country: 'Country',
    Department: 'Department',
    Position: 'Position',
    Clause: 'Terms',
    Currency: 'Currency',

    statistical: 'Reporting',
    turnover: 'Account',
    schedule: 'Job process',

  },
  login: {
    title: 'Login Form',
    logIn: 'Login',
    username: 'Username',
    password: 'Password',
    any: 'any',
    thirdparty: 'Or connect with',
    thirdpartyTips: 'Can not be simulated on local, so please combine you own business simulation! ! !'
  },
  table: {
    dynamicTips1: 'Fixed header, sorted by header order',
    dynamicTips2: 'Not fixed header, sorted by click order',
    dragTips1: 'The default order',
    dragTips2: 'The after dragging order',
    title: 'Title',
    importance: 'Imp',
    type: 'Type',
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    query: 'Search name or notes',
    queryCompany: 'Search company',
    queryContacts: 'Search contacts',
    queryCurrency: 'Search currency',
    querySupply: 'Search product source',
    queryProductCategory: 'Search products category',

    allSelect: 'All',
    inv1: 'invoiced',
    inv2: 'uninvoiced',
    dn1: 'has delivery',
    dn2: 'no delivery',

    company: 'Company',
    contact: 'Contact',
    project: 'Project',
    quote: 'Quote',
    quoteDate: 'Quote date',
    createDate: 'Create date',
    currency: 'Cur',
    total: 'Total',
    inv: 'inv',
    invDetail: 'View invoice',
    invNo: 'No invoice',
    dn: 'dn',
    editDate: 'Edit date',
    editor: 'Editor',
    remark: 'Remark',
    status: 'Status',
    operate: 'Operate',

    productName: 'Product name',
    category: 'Category',
    unit: 'Unit',
    spec: 'Spec',
    desc: 'Desc',
    supply: 'Product source',
    cost: 'Cost',
    total: 'Selling',

    companyName: 'Company',
    country: 'Country',
    address: 'Address',
    tel: 'Tel',
    website: 'Website',

    name:'Name',
    department:'Department',
    position:'Position',
    phone:'Phone',

    name2:'Name',
    language:'Language',
    clause:'Terms',


    enterTip: 'please enter',
    selectTip: 'please select',

    export: 'Export',
    prompt: 'Prompt',
    status: 'Status',
    status0: 'Closure',
    status1: 'Enable',
    actions: 'Actions',
    create: 'Create',
    edit: 'Edit',
    detail: 'Detail',
    delete: 'Delete',
    deleteText: 'Are you sure you want to delete this?',
    goonPrompt: 'Are you sure to continue?',
    deleteSuccess: 'Successfully deleted',
    deleteCancel: 'Undeleted',
    cancelPrompt: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm'
  },

  quotation: {
    title: 'Basic information',
    billTitle: 'Invoices and Delivery',
    project: 'Project',
    quoteDate: 'Quote date',
    company: 'Company',
    companyAddress: 'CompanyAddress',
    currency: 'Currency',
    contact: 'Contact',
    payment: 'Payment',
    paymentRemark: 'Remarks on payment',

    productTitle: 'Product content',
    index: 'Index',
    product: 'Product',
    ProductCategory: 'Cat.',
    number: 'Qty',
    unitPrice: 'Unit Price',
    allPrice: 'All Price',
    cost: 'cost',
    delete: 'delete',
    addRow: 'Add a line',
    total: 'Total',
    totalPrice: 'Total Price',


    clauseTitle: 'Terms',
    clauseCategory: 'Terms Category',
    clauseDetail: 'Terms Detail',

    add: 'Add',
    save: 'Save',
    downloadPdf: 'downloadPdf',

    bill: {
      name: 'Bill',
      add: 'Invoice',
      edit: 'Re-Invoice',
      view: 'View invoice',
      text: 'No invoice',
    },
    delivery: {
      name: 'Delivery',
      add: 'Add delivery',
      edit: 'Edit delivery',
      recipient: 'recipient',
      recipientTel: 'recipientTel',
      recipientAddress: 'recipientAddress',
    },
    otherName1: 'basic information',
    otherName2: 'product content',

  },
}
