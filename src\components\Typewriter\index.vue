<template>
  <div class="typewriter-container">
    <div class="typewriter-text" v-html="enableTypewriter ? currentText : text"></div>
    <div v-if="showCursor && enableTypewriter" class="cursor"></div>
  </div>
</template>

<script>
export default {
  name: 'Typewriter',
  props: {
    text: {
      type: String,
      required: true,
      default: ''
    },
    speed: {
      type: Number,
      default: 10
    },
    showCursor: {
      type: Boolean,
      default: true
    },
    enableTypewriter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentText: '',
      currentIndex: 0,
      isTyping: false
    }
  },
  watch: {
    text: {
      handler(newVal) {
        if (newVal && this.enableTypewriter) {
          this.startTyping()
        } else if (!this.enableTypewriter) {
          this.currentText = this.text
        }
      },
      immediate: true
    }
  },
  methods: {
    startTyping() {
      this.currentText = ''
      this.currentIndex = 0
      this.isTyping = true
      this.typeNextCharacter()
    },
    typeNextCharacter() {
      if (!this.isTyping) return
      
      if (this.currentIndex < this.text.length) {
        this.currentText += this.text[this.currentIndex]
        this.currentIndex++
        setTimeout(this.typeNextCharacter, this.speed)
      } else {
        this.isTyping = false
        this.$emit('typing-completed')
      }
    },
    stopTyping() {
      this.isTyping = false
    }
  },
  beforeDestroy() {
    this.stopTyping()
  }
}
</script>

<style scoped>
.typewriter-container {
  display: flex;
  align-items: flex-start;
  min-height: 150px;
}

.typewriter-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  flex: 1;
}

.cursor {
  width: 3px;
  height: 20px;
  background-color: #333;
  margin-left: 2px;
  animation: blink 0.7s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}
</style> 