<template>
  <div class="container">
      <div class="box">
          <div class="box-line">
              <div id="main">

                  <div class="bg-box bg-box-1">
                      <div class="wave"></div>
                      <div class="bg bg-1"></div>
                  </div>
                  <div class="bg-box bg-box-g">
                      <div class="wave"></div>
                      <div class="bg bg-g"></div>
                  </div>
                  <div id="map">
                      <svg id="mySVG">
                          <path id="path" />
                          <path id="path2" />
                          <path id="path1" />
                          <g id="walkman" transform="translate(0.000000,1280.000000) scale(0.003,-0.003)" fill="#2c3e50"
                              stroke="none">
                              <path d="M4440 12794 c-14 -2 -59 -9 -100 -15 -88 -13 -259 -68 -344 -111
                                                  -279 -141 -496 -384 -598 -668 -58 -160 -82 -389 -59 -551 96 -661 721 -1115
                                                  1399 -1015 602 89 1042 585 1042 1175 0 641 -524 1165 -1185 1186 -71 2 -141
                                                  2 -155 -1z" />
                              <path d="M4300 10199 c-157 -26 -310 -76 -458 -152 -122 -62 -2553 -1739
                                                  -2605 -1797 -71 -79 -94 -135 -162 -392 -35 -134 -114 -434 -175 -668 -61
                                                  -234 -145 -551 -185 -705 -132 -502 -128 -482 -122 -569 19 -290 352 -497 676
                                                  -421 108 26 188 71 272 155 97 96 116 138 185 403 30 117 77 298 104 402 27
                                                  105 106 408 174 674 69 267 131 490 138 497 24 22 831 574 835 570 2 -2 -188
                                                  -725 -422 -1607 -545 -2052 -764 -2878 -825 -3108 -27 -101 -57 -195 -66 -210
                                                  -10 -14 -354 -506 -766 -1093 -411 -587 -766 -1095 -787 -1128 -57 -87 -101
                                                  -218 -108 -315 -11 -175 56 -344 191 -480 168 -170 370 -249 631 -249 160 0
                                                  276 26 398 88 111 57 181 110 256 193 74 83 1704 2417 1811 2593 87 144 50 18
                                                  326 1102 l167 658 46 -43 c25 -23 420 -390 877 -814 773 -718 832 -775 842
                                                  -815 6 -24 131 -578 277 -1233 147 -655 277 -1215 290 -1246 88 -218 295 -391
                                                  550 -460 119 -33 335 -33 460 0 175 45 312 125 425 248 111 121 177 304 167
                                                  459 -5 75 -576 2652 -607 2741 -10 30 -31 76 -46 101 -17 30 -418 439 -1108
                                                  1130 -704 705 -1080 1089 -1079 1101 3 24 531 2001 542 2026 6 16 29 -17 124
                                                  -178 65 -108 170 -286 234 -394 84 -143 132 -213 172 -253 46 -45 217 -149
                                                  971 -590 503 -294 944 -546 980 -561 229 -93 478 -53 645 103 187 174 191 434
                                                  11 611 -52 52 -182 131 -909 556 l-849 496 -613 1035 c-591 997 -617 1038
                                                  -700 1128 -201 214 -474 361 -755 407 -104 16 -339 19 -430 4z" />
                          </g>
                      </svg>
                      <div class="point-box" id="point-box"></div>
                  </div>
                  <div class="btn-box" id="btnBox">
                      <!-- <button id="startBtn1" class="button" @click="animFunc('GF', point1)">13 Inspiration Loft</button> -->
                  </div>

              </div>
          </div>
      </div>
      <div class="btn-parent">

          <div class="btn-panel" v-for="item of dataList">
              <p class="title">{{ item.name }}</p>
              <div class="btn-box">
                  <!-- <div class="btn-color" v-for="single of item.classifications" @click="showPopup(single.id)">{{
                      single.name }}</div> -->
                  <div v-for="item2 of item.classifications">
                      <p class="title">{{ item2.name_tc }}</p>
                      <div class="btn-color" v-for="single of item2.points" @click="animFunc(single.floor, single.points)">{{ single.name_tc }}
                      </div>
                  </div>
              </div>
          </div>

      </div>
  </div>
</template>

<script>
import Snap from 'snapsvg-cjs-patched';
import axios from 'axios'
export default {
  data() {
      return {
          loading: false,
          popupShow: false,//按鈕弹窗
          dataList: [],//按钮数据列表
          dataDetail: [],//按钮详情列表
          // map
          anim: null,
          sw: window.innerWidth,
          scaleX: 1,
          walkmanBaseSize: 0.003,
          s: null,
          walkman: null,
          walkmanSize: [17.37, 25.58],
          path: null,
          circle: null,
          circle2: null,
          text: '',
          floor: 1,
          p1Count: 0,
          p2Count: 0,
          ii: 5,
          i1: 0,

          BASEURL: "https://api.honkai.co",
          data: [], // 坐标数组
          point1: [[1061, 450], [1017, 438], [948, 459], [914, 486], [889, 512], [896, 551], [869, 582], [835, 602], [811, 596], [799, 551], [77777, 551], [870, 1385], [885, 1380], [864, 1343], [795, 1382], [741, 1475], [761, 1494], [777, 1491], [784, 1465], [798, 1416], [824, 1410]],
          shopPointList: [[[1061, 450], [1017, 438], [948, 459]],
          [[1061, 450], [1017, 438], [948, 459], [914, 486], [889, 512], [896, 551], [869, 582], [835, 602], [811, 596], [799, 551]],
          [[1061, 450], [1017, 438], [948, 459], [914, 486], [889, 512], [896, 551], [869, 582], [835, 602], [782, 629], [805, 675], [782, 687]]],

      };
  },
  mounted() {
      // 请求接口数据
      this.getData()
      //
      // map 属性
      //
      let start = [1061, 220];
      // this.sw = this.sw * 0.84
      this.scaleX = 1;
      // this.scaleX = this.sw / 1839.65;
      // 初始化id为 mySVG 的图
      this.s = Snap('#mySVG');
      this.walkman = Snap('#walkman');
      this.path = Snap('#path');
      this.path1 = Snap('#path1');
      this.path2 = Snap('#path2');
      // 画一条线，用于显示圆点走过的路径
      let path = this.s.path();
      // 画一个圆，在坐标 50，50 的位置，半径为 10
      this.circle = this.s.circle(-50 * this.scaleX, -50 * this.scaleX, 10 * this.scaleX);
      this.circle2 = this.s.circle(-50 * this.scaleX, -50 * this.scaleX, 10 * this.scaleX);

      this.text = this.s.text(-50, -50, "");
      // 将 g 包裹的 path 元素移到初始位置
      this.walkman.transform(`translate(${start[0] + (this.walkmanSize[0] / 2)}, ${start[1]}) scale(-${this.walkmanBaseSize * this.scaleX},-${this.walkmanBaseSize * this.scaleX})`);
  },
  methods: {
      // map相关
      async animFunc(num, num2) {
          switch (num) {
              case 'GF':
                  await this.clickAnim(num2, 'GF');
                  this.path.attr({
                      stroke: 'red',
                      fill: 'none',
                      'stroke-dasharray': '0',
                      'stroke-width': 3
                  }); // 初始化路径
                  break;
              case '1F':
                  await this.clickAnim(num2);
                  this.path.attr({
                      stroke: 'red',
                      fill: 'none',
                      'stroke-dasharray': '0',
                      'stroke-width': 3
                  }); // 初始化路径
                  break;
              default:
                  await this.clickAnim(num2);
                  this.path.attr({
                      stroke: 'red',
                      fill: 'none',
                      'stroke-dasharray': '0',
                      'stroke-width': 3
                  }); // 初始化路径
                  break;
          }
      },
      modifyArray(array) {
          return array;
      },

      // 按钮的方法
      async clickAnim(point, target) {
          let that = this; // 保存Vue组件实例到self
          this.path.attr({ stroke: "#f0f", fill: "none", "stroke-dasharray": "5 3", "stroke-width": 3 }); // 初始化路径
          // 如果有动画正在运行，则停下来

          that.floor = 1
          that.p1Count = 0
          that.p2Count = 0
          if (this.anim) {
              this.anim.stop()
          }
          await this.animateCircle(point, target)

      },
      async animateCircle(data, target) {
          let that = this; // 保存Vue组件实例到self

          let coordinates = that.modifyArray([...data]);
          // 单位距离内的动画持续时长
          let startPoint = [coordinates[0][0], coordinates[0][1]];
          const animDuration = 800;
          that.circle.attr({ cx: startPoint[0] * that.scaleX, cy: startPoint[1] * that.scaleX, style: 'fill:blue' }); // 初始化起始圆点位置
          if (target) {
              that.circle2.attr({ cx: -500, cy: -500, style: 'fill:white' }); // 初始化終點圆点位置
          } else {
              that.circle2.attr({ cx: coordinates[coordinates.length - 1][0] * that.scaleX, cy: coordinates[coordinates.length - 1][1] * that.scaleX, style: 'fill:red' }); // 初始化終點圆点位置
          }

          let p = `M ${startPoint[0] * that.scaleX} ${startPoint[1] * that.scaleX}`;
          let p1 = `M ${startPoint[0] * that.scaleX} ${startPoint[1] * that.scaleX}`;
          let p2 = `M ${startPoint[0] * that.scaleX} ${startPoint[1] * that.scaleX}`;
          that.path.attr({ d: p, }); // 初始化路径
          that.path1.attr({ d: p, }); // 初始化路径
          that.path2.attr({ d: p, }); // 初始化路径


          // 将 g 包裹的 path 元素移到初始位置
          that.walkman.transform(`translate(${startPoint[0] * that.scaleX}, ${startPoint[1] * that.scaleX}) scale(-${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);

          function animate(index) {
              if (index >= coordinates.length) return;
              // 下一个点的坐标
              let nextCoord = coordinates[index];
              let nextCoord2;
              if (index < coordinates.length - 1) {
                  // 下下一个点的坐标
                  nextCoord2 = coordinates[index + 1];
              } else {
                  nextCoord2 = coordinates[index];
              }

              // 获取两个点之间的距离
              let distance = getDistance(nextCoord, nextCoord2);
              // 每个动画的持续时间，按距离进行配比
              let animDur = distance * animDuration / 100;
              // 定义向左或者向右
              let right = nextCoord2[0] - nextCoord[0] > 0;

              if (nextCoord2[0] == 88888 || nextCoord2[0] == 99999) {
                  let name = nextCoord2[0] == 99999 ? 'down' : 'up';
                  that.move(name, coordinates[index + 2]);
                  nextCoord2 = coordinates[index + 2];
                  coordinates[index + 1] = coordinates[index + 2];
                  p = `M ${nextCoord2[0] * that.scaleX} ${nextCoord2[1] * that.scaleX}`;
                  that.path.attr({ d: p }); // 初始化路径

                  that.circle.attr({ cx: nextCoord2[0] * that.scaleX, cy: nextCoord2[1] * that.scaleX, style: 'fill:blue' }); // 初始化起始圆点位置
                  that.circle2.attr({ cx: coordinates[coordinates.length - 1][0] * that.scaleX, cy: coordinates[coordinates.length - 1][1] * that.scaleX, style: 'fill:red' }); // 初始化終點圆点位置
                  that.text = name;
                  setTimeout(() => {
                      animate(index + 1);
                  }, 1500);
              } else if (nextCoord2[0] == 77777) {
                  nextCoord2 = coordinates[index + 2]
                  nextCoord = [807, 492]
                  coordinates[index + 1] = coordinates[index + 2]
                  if (that.p1Count === 0) {
                      console.log(that.p1Count);
                      p1 = `M ${nextCoord[0] * that.scaleX} ${nextCoord[1] * that.scaleX}`;
                      that.path1.attr({ stroke: "#f0f", fill: "none", "stroke-dasharray": "5 3", "stroke-width": 3 }); // 初始化路径
                  }
                  that.p1Count++
                  that.floor = 0

                  that.walkman.transform(`translate( ${-300 * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}  , -${-300 * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}" ) scale(${that.walkmanBaseSize * that.scaleX} ,-${that.walkmanBaseSize * that.scaleX})`);
                  // 定义动画函数为 anim
                  setTimeout(() => {
                      // 定义动画函数为anim
                      that.anim = Snap.animate(
                          nextCoord,
                          nextCoord2,
                          value => {
                              // 判断小人的方向，如果是左就是负，右就是正
                              if (right) {
                                  that.walkman.transform(`translate(${value[0] * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                              } else {
                                  that.walkman.transform(`translate(${value[0] * that.scaleX + 1 * (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(-${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                              }
                              p1 = p1 + `L ${(value[0] * that.scaleX)} ${(value[1] * that.scaleX)}`;
                              that.path1.attr({
                                  d: p1
                              });
                          },
                          3000,
                          function () {
                              animate(index + 1);
                          }
                      );
                  }, 500)
              } else if (nextCoord2[0] == 77788) {
                  nextCoord2 = coordinates[index + 2]
                  nextCoord = [755, 492]
                  coordinates[index + 1] = coordinates[index + 2]
                  if (that.p1Count === 0) {
                      console.log(that.p1Count);
                      p1 = `M ${nextCoord[0] * that.scaleX} ${nextCoord[1] * that.scaleX}`;
                      that.path1.attr({ stroke: "#f0f", fill: "none", "stroke-dasharray": "5 3", "stroke-width": 3 }); // 初始化路径
                  }
                  that.p1Count++
                  that.floor = 0

                  that.walkman.transform(`translate( ${-300 * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}  , -${-300 * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}" ) scale(${that.walkmanBaseSize * that.scaleX} ,-${that.walkmanBaseSize * that.scaleX})`);
                  // 定义动画函数为 anim
                  setTimeout(() => {
                      // 定义动画函数为anim
                      that.anim = Snap.animate(
                          nextCoord,
                          nextCoord2,
                          value => {
                              // 判断小人的方向，如果是左就是负，右就是正
                              if (right) {
                                  that.walkman.transform(`translate(${value[0] * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                              } else {
                                  that.walkman.transform(`translate(${value[0] * that.scaleX + 1 * (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(-${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                              }
                              p1 = p1 + `L ${(value[0] * that.scaleX)} ${(value[1] * that.scaleX)}`;
                              that.path1.attr({
                                  d: p1
                              });
                          },
                          3000,
                          function () {
                              animate(index + 1);
                          }
                      );
                  }, 500)
              }
              else if (that.floor === 0) {
                  if (that.p2Count === 0) {
                      p2 = `M ${nextCoord2[0] * that.scaleX} ${nextCoord2[1] * that.scaleX}`;
                      that.path2.attr({ d: p2, stroke: "red", fill: "none", "stroke-dasharray": "0", "stroke-width": 3 }); // 初始化路径
                  }
                  that.p2Count++
                  // 定义动画函数为anim
                  that.anim = Snap.animate(
                      nextCoord,
                      nextCoord2,
                      value => {
                          // 判断小人的方向，如果是左就是负，右就是正
                          if (right) {
                              that.walkman.transform(`translate(${value[0] * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                          } else {
                              that.walkman.transform(`translate(${value[0] * that.scaleX + 1 * (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(-${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                          }

                          p2 = p2 + `L ${(value[0] * that.scaleX)} ${(value[1] * that.scaleX)}`;
                          that.path2.attr({
                              d: p2
                          });
                      },
                      animDur,
                      function () {
                          animate(index + 1);
                      }
                  );

              } else {
                  // 定义动画函数为 anim
                  that.anim = Snap.animate(
                      nextCoord,
                      nextCoord2,
                      value => {
                          // 判断小人的方向，如果是左就是负，右就是正
                          if (right) {
                              that.walkman.transform(`translate(${value[0] * that.scaleX - (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                          } else {
                              that.walkman.transform(`translate(${value[0] * that.scaleX + 1 * (that.walkmanSize[0] * that.scaleX / 2)}, ${value[1] * that.scaleX}) scale(-${that.walkmanBaseSize * that.scaleX},-${that.walkmanBaseSize * that.scaleX})`);
                          }
                          p = p + `L ${(value[0] * that.scaleX)} ${(value[1] * that.scaleX)}`;
                          that.path.attr({
                              d: p
                          });
                      },
                      animDur,
                      () => {
                          animate(index + 1);
                      }
                  );
              }
          }

          function getDistance(p1, p2) {
              const a = p1[0] - p2[0];
              const b = p1[1] - p2[1];

              const c = Math.sqrt(a * a + b * b);
              return c;
          }
          if (that.floor != 1) {
              await that.moveStart();
              setTimeout(() => {
                  console.log('move over');
                  animate(0);
              }, 1000);
          } else {
              animate(0);
          }
      },
      async move(arrow, nextCoord) {
          console.log(arrow);

          const bgg = document.querySelector('.bg-box-g');
          const bg1 = document.querySelector('.bg-box-1');
          const height = bgg.offsetHeight / 4;
          if (arrow == 'up') {
              bgg.style.opacity = 0;
              bgg.style.transform = `translateY(${height}px)`;
              bg1.style.opacity = 1;
              bg1.style.transform = 'translateY(0)';
              this.floor += 1
          } else {
              bgg.style.opacity = 1;
              bgg.style.transform = 'translateY(0)';
              bg1.style.opacity = 0;
              bg1.style.transform = `translateY(-${height}px)`;
              this.floor -= 1

          }
      },

      // 楼层初始化
      async moveStart() {

          const bgg = document.querySelector('.bg-box-g');
          const bg1 = document.querySelector('.bg-box-1');
          const height = bgg.offsetHeight / 10;
          bgg.style.opacity = 0;
          bgg.style.transform = `translateY(${height}px)`;
          bg1.style.opacity = 1;
          bg1.style.transform = 'translateY(0)';
          this.floor = 1
      },
      async getData() {
          const apiUrl = "https://novoland.esc-map.com/api/maptypes?populate[0]=classifications&populate[1]=classifications.points"

          await axios.get(apiUrl)
              .then((response) => {
                  this.dataList = response.data.results
                  console.log(this.dataList);

              })
              .catch((error) => console.log(error));
      },
      async getDataDetail(id) {
          const apiUrl = `https://novoland.esc-map.com/api/classifications/${id}?populate=points`

          await axios.get(apiUrl)
              .then((response) => {
                  this.dataDetail = response.data.results
                  console.log(this.dataDetail);

              })
              .catch((error) => console.log(error));
      },
      async showPopup(id) {
          this.loading = true
          this.dataDetail = []
          this.popupShow = true
          await this.getDataDetail(id)
          this.loading = false

      },

      goBack() {
          this.popupShow = false
      }

  },
}



</script>
<style lang="scss" scoped>
@import './css/index.css';

.container {
  position: relative;
  .box {
      position: relative;
      // margin: 12px 0;
      background-color: #fff;
      // border-radius: 30px;
      // padding: 0 10px 8px 10px;
      // box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
      // border: 1px solid transparent;


      .box-line {
          position: relative;
          // margin: 10px 0;
          // padding: 20px;
          // border: 1px solid #DC7883;
          // border-radius: 12px;
          overflow: hidden;
      }

  }
}
</style>
