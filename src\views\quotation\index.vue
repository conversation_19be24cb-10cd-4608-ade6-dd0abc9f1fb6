<template>
  <div class="app-container">
    <h1>{{ $t('route.quotation') }}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item label="">
        <el-input v-model="listQuery['filters[$and][0][company][name][$contains]']"
          :placeholder="$t('table.queryCompany')" />
      </el-form-item>
      <el-form-item label="">
        <el-input v-model="listQuery['filters[$and][1][contact_person][name][$contains]']"
          :placeholder="$t('table.queryContacts')" />
      </el-form-item>
      <el-form-item label="">
        <el-input v-model="listQuery['filters[$and][2][currency][name][$contains]']"
          :placeholder="$t('table.queryCurrency')" />
      </el-form-item>
      <el-form-item label="">
        <el-select v-model="billsSelect" clearable :placeholder="$t('table.inv')">
          <el-option :key="2" :label="$t('table.allSelect')" :value="2">
          </el-option>
          <el-option :key="1" :label="$t('table.inv1')" :value="1">
          </el-option>
          <el-option :key="0" :label="$t('table.inv2')" :value="0">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-select v-model="deliverySelect" clearable :placeholder="$t('table.dn')">
          <el-option :key="2" :label="$t('table.allSelect')" :value="2">
          </el-option>
          <el-option :key="1" :label="$t('table.dn1')" :value="1">
          </el-option>
          <el-option :key="0" :label="$t('table.dn2')" :value="0">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">{{ $t('table.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">{{ $t('table.reset') }}</el-button>

        <router-link class="create" :to="'/quotation/create/'">
          <el-button type="primary" size="small" icon="el-icon-plus">
            {{ $t('table.add') }}
          </el-button>
        </router-link>
        <el-button type="primary" style="margin-left:10px" plain icon="el-icon-download" @click="handleDownload()"
          :loading="downloadLoading">Export Excel</el-button>
        <!-- <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">新增</el-button> -->
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe @sort-change="handlesortChange">
      <el-table-column prop="company" :label="$t('table.company')" width="170" sortable="custom" />
      <el-table-column prop="contact_person" :label="$t('table.contact')" width="100" sortable="custom" />
      <el-table-column prop="name" :label="$t('table.project')" width="250" sortable="custom" />
      <el-table-column prop="uuid" :label="$t('table.quote')" width="110" sortable="custom" />
      <el-table-column prop="date" :label="$t('table.quoteDate')" width="100" />
      <el-table-column prop="currency" :label="$t('table.currency')" width="60" />
      <el-table-column prop="allPrice" align="right" :label="$t('table.total')" width="110">
        <template slot-scope="scope">
          {{ scope.row.allPrice | getArea }}
        </template>
      </el-table-column>

      <el-table-column prop="bill" align="center" :label="$t('table.inv')" width="90">
        <template slot-scope="scope">
          <template>
            <el-button @click="handleClickBill('bill', scope.row.id)" type="text" v-if="scope.row.bill">{{
                scope.row.bill.uuid
            }}
            </el-button>
            <span v-if="!scope.row.bill">{{ scope.row.bill }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="delivery" align="center" :label="$t('table.dn')" width="50">
        <template slot-scope="scope">
          <template>
            <el-button @click="handleClickBill('delivery', scope.row.id)" type="text" v-if="scope.row.delivery > 0">{{
                scope.row.delivery
            }}
            </el-button>
            <span v-if="scope.row.delivery == 0">{{ scope.row.delivery }}</span>
          </template>
        </template>
      </el-table-column>

      <el-table-column prop="updatedAt" width="140" :label="$t('table.editDate')" />
      <el-table-column prop="updatedBy" :label="$t('table.editor')" width="70" />
      <el-table-column prop="stateType" :label="$t('table.status')" width="70" />
      <el-table-column :label="$t('table.operate')" width="200">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{ $t('table.edit')
          }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
                $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />


    <el-dialog :title="tableTextMap[dialogTableStatus]" :visible.sync="dialogTableVisible" width="90%">
      <otherTable :tableId="tableId" :dialogState="dialogTableStatus" :key="time" />
    </el-dialog>


  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'
import otherTable from '@/components/otherTable'

export default {
  name: 'Product',
  components: { Pagination, otherTable },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::quotation.quotation/',
      statisticalUrl: '/content-manager/collection-types/api::statistical.statistical/',
      companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      currencyUrl: '/content-manager/collection-types/api::currency.currency?filters[$and][0][state][$eq]=true',
      list: [],
      downloadLoading: false,
      totle: 0,
      time: '',
      listLoading: false,
      loading: false,
      // 弹框
      dialogFormVisible: false,
      // table弹框
      dialogTableVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      // 弹框属于發票還是發貨單的列表
      dialogTableStatus: '',
      tableTextMap: {
        bill: '發票',
        delivery: '送貨單'
      },
      parms: {
        searchName: ''
      },
      billsSelect: '',
      deliverySelect: '',
      // 公司
      companyOptions: [],
      // 职位
      positionOptions: [],
      // 部门
      departmentOptions: [],
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'uuid:DESC',
        populate: '*',
        _q: ''
      },
      // 父子table傳值
      tableId: '',
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: this.$t('table.enterTip'), trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: this.$t('table.selectTip'), trigger: 'blur' }
        ],
        // 部门
        department: [
          { required: true, message: this.$t('table.selectTip'), trigger: 'blur' }
        ],
        // 职位
        position: [
          { required: true, message: this.$t('table.selectTip'), trigger: 'blur' }
        ],
        // 狀態
        stateType: [
          { required: true, message: this.$t('table.selectTip'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList(this.listQuery)
  },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            // 公司名称
            company: res[index].company ? res[index].company.name : '',
            company_value: res[index].company ? res[index].company.id : '',
            // 地址
            address: res[index].company ? res[index].company.address : '',
            // 聯繫人
            contact_person: res[index].contact_person ? res[index].contact_person.name : '',
            contact_person_value: res[index].contact_person ? res[index].contact_person.id : '',
            // 付款方式
            payment_name: res[index].payment_name ? res[index].payment_name : '',
            payment_detail: res[index].payment_name ? res[index].payment_detail : '',
            // 條文詳情
            clause: res[index].clause_name,
            clause_detail: res[index].clause_detail,

            // 貨幣
            currency: res[index].currency ? res[index].currency.name : "",
            currency_value: res[index].currency ? res[index].currency.id : "",
            // 項目名稱3
            name: res[index].name,
            // 報價日期
            date: res[index].date,
            // 報價單號
            id: res[index].id,
            // 修改次數 R
            uuid: res[index].uuid,
            // 總價
            allPrice: res[index].allPrice,
            // 發票
            bill: res[index].bill ? res[index].bill : '',
            // 送貨單
            delivery: res[index].deliveries ? res[index].deliveries.count : 0,
            // 狀態

            // 產品詳情
            content: res[index].content,
            // 流水統計
            statistical: res[index].statistical,

            // 狀態
            state: res[index].state,
            stateType: res[index].state ? this.$t('table.status1') : this.$t('table.status0'),
            // 修改時間
            updatedAt: timeChange(res[index].updatedAt),
            // 修改人
            updatedBy: res[index].updatedBy? res[index].updatedBy.firstname: 'null'

          }
          console.log(arr)
          arrList.push(arr)
        }
        this.list = arrList
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字
    async getRelation(url, p) {
      let params = ''
      let rt
      if (p) {
        params = {
          'filters[$and][1][name][$contains]': p
        }
      }
      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name
          }
          arrList.push(arr)
        }
        this.listLoading = false
        rt = arrList
      })
      return rt
    },
    // 點擊發票數或送貨單數列表中的編輯中的事件
    handleClickBill(type, index) {
      this.listLoading = true
      this.time = new Date().getTime()
      this.dialogTableStatus = type
      this.tableId = index
      console.log(this.dialogTableStatus);
      this.dialogTableVisible = true
      this.listLoading = false
    },
    // 點擊quotation修改的事件
    handleClick(row) {
      this.listLoading = true
      this.$router.push({ path: '/quotation/edit', query: { id: row.id } })
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.baseUrl, 'delete', row.id, '').then(() => {
          if (row.statistical) {
            postData(this.statisticalUrl, 'delete', row.statistical.id, '').then(() => {
            })
          }
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      this.billsSelectFunction()
      this.deliverySelectFunction()

      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.billsSelect = ''
      this.deliverySelect = ''
      this.billsSelectFunction()
      this.deliverySelectFunction()
      this.listQuery = {
        page: 1,
        pageSize: 20,
        sort: 'uuid:DESC',
        _q: ''
      }
      this.getList(this.listQuery)
    },
    billsSelectFunction() {
      switch (this.billsSelect) {
        case 0:
          delete this.listQuery['filters[$and][0][bills][id][$notNull]']
          this.listQuery['filters[$and][0][bills][id][$null]'] = true
          break;
        case 1:
          delete this.listQuery['filters[$and][0][bills][id][$null]']
          this.listQuery['filters[$and][0][bills][id][$notNull]'] = true
          break;
        case 2:
          delete this.listQuery['filters[$and][0][bills][id][$notNull]']
          delete this.listQuery['filters[$and][0][bills][id][$null]']
          break;

        default:
          delete this.listQuery['filters[$and][0][bills][id][$notNull]']
          delete this.listQuery['filters[$and][0][bills][id][$null]']
          break;
      }
    },
    deliverySelectFunction() {
      switch (this.deliverySelect) {
        case 0:
          delete this.listQuery['filters[$and][0][deliveries][id][$notNull]']
          this.listQuery['filters[$and][0][deliveries][id][$null]'] = true
          break;
        case 1:
          delete this.listQuery['filters[$and][0][deliveries][id][$null]']
          this.listQuery['filters[$and][0][deliveries][id][$notNull]'] = true
          break;
        case 2:
          delete this.listQuery['filters[$and][0][deliveries][id][$notNull]']
          delete this.listQuery['filters[$and][0][deliveries][id][$null]']
          break;

        default:
          delete this.listQuery['filters[$and][0][deliveries][id][$notNull]']
          delete this.listQuery['filters[$and][0][deliveries][id][$null]']
          break;
      }
    },
    changeCountry(item) {
      this.form.currency = item.currency
    },
    // 新增或者修改时，新增电话或者手机的方法
    add(t) {
      if (t === 'telephone') {
        this.form.telephone.push('')
      } else if (t === 'phone') {
        this.form.phone.push('')
      }
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(t, delete_index) {
      if (t === 'telephone') {
        this.form.telephone.splice(delete_index, 1)
      } else if (t === 'phone') {
        this.form.phone.splice(delete_index, 1)
      }
    },
    // 排序的方法
    handlesortChange(column) {
      let target = ''
      switch (column.prop) {
        case 'company':
          target = 'company.name'
          break;
        case 'contact_person':
          target = 'contact_person.name'
          break;
        case 'name':
          target = 'name'
          break;
        case 'uuid':
          target = 'uuid'
          break;

        default:
          break;
      }
      if (column.order == "ascending") {
        this.listQuery.sort = target + ":ASC"
      }
      if (column.order == "descending") {
        this.listQuery.sort = target + ":DESC"
      }
      this.getList(this.listQuery)
    },
    // 導出Excel
    handleDownload() {
      this.$confirm(this.$t('table.goonPrompt'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = [this.$t('table.company'), this.$t('table.contact'), this.$t('table.project'), this.$t('table.quote'), this.$t('table.quoteDate'), this.$t('table.currency'), this.$t('table.total'), this.$t('table.inv'), this.$t('table.dn'), this.$t('table.editDate'), this.$t('table.editor')]
          const filterVal = ['company', 'contact_person', 'name', 'uuid', 'date', 'currency', 'allPrice', "bill", 'delivery', 'updatedAt', 'updatedBy']
          const data = this.formatJson(filterVal, this.list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.$t('route.quotation'),
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message:this.$t('table.cancelPrompt')
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else if (j === 'bill') {
          return v[j].uuid
        }
        else {
          return v[j]
        }
      }))
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}

.create {
  margin-left: 10px;
}

.dialog {

  .el-dialog {
    width: 100% !important;
    margin-left: 2vw;
    margin-right: 2vw;
  }
}
</style>
