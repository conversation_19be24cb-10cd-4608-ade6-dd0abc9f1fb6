<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="150px">
      <div class="box" style="width:350px">
        <h3>{{ $t('quotation.title') }}</h3>
        <el-form-item :label="$t('quotation.project')" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item :label="$t('quotation.quoteDate')" prop="date">
          <el-date-picker v-model="form.date" type="date" placeholder="Please select" style="width:200px">
          </el-date-picker>
        </el-form-item>


        <el-form-item :label="$t('quotation.company')" prop="company">
          <el-select v-model="form.company" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :remote-method="remoteMethod_companyOptions" :loading="loading"
            default-first-option @change="changeCompany">
            <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.companyAddress')">
          <el-input v-model="form.address" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>

        <el-form-item :label="$t('quotation.currency')" prop="currency">
          <el-select v-model="form.currency" clearable filterable reserve-keyword class="filter-item"
            placeholder="Please select" :loading="loading" default-first-option>
            <el-option v-for="item in currencyOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.contact')" prop="company">
          <el-select v-model="form.contact_person" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :remote-method="remoteMethod_contactPersonOptions" :loading="loading"
            default-first-option>
            <el-option v-for="item in contactPersonOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>



        <el-form-item :label="$t('quotation.payment')" prop="company">
          <el-select v-model="form.payment_name" clearable filterable remote reserve-keyword class="filter-item"
            placeholder="Please select" :loading="loading" default-first-option @change="changePayment">
            <el-option v-for="item in paymentOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('quotation.paymentRemark')">
          <el-input v-model="form.payment_detail" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
      </div>


      <div class="box">
        <h3>{{ $t('quotation.productTitle') }}</h3>
        <el-table v-loading="listLoading" show-summary :summary-method="getSummaries" :data="form.content" border fit
          highlight-current-row style="width: 1400px" :cell-style="cellStyle">
          <el-table-column align="center" :label="$t('quotation.index')" min-width="30px">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>


          <el-table-column align="left" min-width="200px" :label="$t('quotation.product')">
            <template slot-scope="scope">
              <template>
                <el-select v-model="form.content[scope.$index].label" clearable filterable remote reserve-keyword
                  class="filter-item" placeholder="Please select" :remote-method="remoteMethod_product"
                  @change="changeContent(scope.row, scope.$index)" :loading="loading" default-first-option>
                  <el-option v-for="(item, i) in productOptions" :key="item.value" :label="item.label" :value="item" />
                </el-select>

                <el-input class="describe" v-model="scope.row.describe" :autosize="{ minRows: 2, maxRows: 4 }"
                  type="textarea" />
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" min-width="75px" :label="$t('quotation.ProductCategory')">
            <template slot-scope="{row}">
              <template>
                <span>{{ row.product_category }}</span>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="num" min-width="100px" :label="$t('quotation.number')">
            <template slot-scope="{row}">
              <template>
                <el-input-number v-model="row.num" :min="1" :max="999" size="small"
                  label="please input"></el-input-number>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="price" min-width="70px" :label="$t('quotation.unitPrice')">
            <template slot-scope="{row}">
              <template>
                <el-input v-model="row.price" size="small" />
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="totalPrice" min-width="80px" :label="$t('quotation.allPrice')">
            <template slot-scope="scope">
              <template>
                <span>{{ scope.row.totalPrice = scope.row.num && scope.row.price ? scope.row.num * scope.row.price :
                    ''|getArea
                }}</span>
              </template>
            </template>
          </el-table-column>



          <el-table-column align="center" prop="totalCost" min-width="80px" :label="$t('quotation.cost')">
            <template slot-scope="scope">
              <template>
                <span class="cost">{{ scope.row.totalCost = scope.row.num && scope.row.cost ? scope.row.num *
                    scope.row.cost :
                    0|getArea
                }}</span>
              </template>
            </template>
          </el-table-column>

          <el-table-column align="center" label="移除" width="120" :label="$t('quotation.delete')">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem(scope.$index)" />
            </template>
          </el-table-column>


        </el-table>

        <el-button type="primary" size="small" icon="el-icon-plus" plain class="add" @click="add">
          {{ $t('quotation.addRow') }}
        </el-button>
      </div>



      <div class="box">
        <h3>{{ $t('quotation.clauseTitle') }}</h3>
        <el-form-item :label="$t('quotation.clauseCategory')" prop="clause_type">
          <el-select v-model="form.clause" class="filter-item" placeholder="Please select" @change="changeClause">
            <el-option v-for="item in clauseOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('quotation.clauseDetail')">
          <el-input v-model="form.clause_detail" :autosize="{ minRows: 4, maxRows: 20 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
      </div>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" size="medium" @click="dialogConfirm('create')">
        {{ $t('quotation.add') }}
      </el-button>
    </div>

  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn, getRelation, fillZero } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::quotation.quotation/',
      statisticalUrl: '/content-manager/collection-types/api::statistical.statistical/',
      companyUrl: '/content-manager/collection-types/api::company.company?filters[$and][0][state][$eq]=true',
      contactPersonUrl: '/content-manager/collection-types/api::contact-person.contact-person?filters[$and][0][state][$eq]=true',
      paymentUrl: '/content-manager/collection-types/api::payment-method.payment-method?filters[$and][0][state][$eq]=true',
      productUrl: '/content-manager/collection-types/api::product.product?filters[$and][0][state][$eq]=true',
      clausetUrl: '/content-manager/collection-types/api::clause.clause?filters[$and][0][state][$eq]=true',
      currencytUrl: '/content-manager/collection-types/api::currency.currency?filters[$and][0][state][$eq]=true',
      list: [],
      listLoading: false,
      loading: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 公司
      companyOptions: [],
      // 貨幣
      currencyOptions: [],
      // 聯繫人
      contactPersonOptions: [],
      // 付款方式
      paymentOptions: [],
      // 產品
      productOptions: [],
      // 條文
      clauseOptions: [],
      form: {},
      formRow: {},
      uuid: '',

      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      },
      // 公司的請求字段
      companyQarams: {},
      // 聯繫人的請求字段
      contactPersonQarams: {},
      baseContent: {
        "id": "",
        "productCategory": "",
        "num": "",
        "price": "",
        "totalPrice": "",
        "cost": ""
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 日期
        date: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 公司
        company: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 貨幣
        currency: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 联系人
        contact_person: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 付款方式
        payment: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 條文內容
        clause: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    // this.getList(this.listQuery)
    // 獲取公司
    this.remoteMethod_companyOptions()
    // 獲取付款方式
    this.remoteMethod_paymentOptions()
    // 獲取貨幣
    this.remoteMethod_currencyOptions()
    //设置默认今天
    this.$set(this.form, "date", this.dateFormat("YYYY-mm-dd", new Date()))
    // 設置第一個產品的格式
    this.form.content = [{
      "id": "",
      "productCategory": "",
      "num": "",
      "price": "",
      "totalPrice": "",
      "cost": ""
    }],
      // 獲取條文內容
      this.getRelation(this.clausetUrl).then((result) => {
        const res = result
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            language: res[index].language,
            detail: res[index].detail
          }
          arrList.push(arr)
        }
        this.clauseOptions = arrList
      })
  },
  methods: {
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        };
      };
      return fmt;
    },

    // 獲取關聯分類，第一個參數是url，第二個參數是搜索名字,第三個是限定條件
    async getRelation(url, p, target) {
      let params = ''
      let rt
      if (p && !target) {
        params = {
          'filters[$and][0][name][$contains]': p
        }
      } else if (p && target) {
        params = {
          'filters[$and][0][name][$contains]': p,
          'filters[$and][1][company][id][$eq]': target,
        }
      } else if (!p && target) {
        params = {
          'filters[$and][0][company][id][$eq]': target,
        }
      }

      this.listLoading = true
      await getData(url, params).then(response => {
        const res = response.results
        rt = res
        this.listLoading = false
      })
      return rt
    },
    // 提交創建的事件
    async createData() {
      // 清除空的content
      this.clearContent();
      await this.getUid();
      if (this.form.content.length) {
        this.$refs['form'].validate((valid) => {
          console.log(this.form);
          if (valid) {
            console.log('allCost');
            console.log( this.form.allCost);
            const temp = {
              name: this.form.name ? this.form.name : '',
              // 如果是關聯表，則用方法去判斷顯示什麼
              company: relationCreateReturn(this.form.company.value),
              contact_person: relationCreateReturn(this.form.contact_person.value),
              currency: relationCreateReturn(this.form.currency.value ? this.form.currency.value : this.form.currency_value),

              // bill: relationCreateReturn(this.form.bill.value),
              // 數組格式需判斷是否有內容，沒有則用[""]標識
              content: this.form.content.length ? this.form.content : [''],

              payment_name: this.form.payment_name.label ? this.form.payment_name.label : '',
              payment_detail: this.form.paymentRemark ? this.form.paymentRemark : '',
              clause_name: this.form.clause ? this.form.clause.label : '',
              clause_detail: this.form.clause_detail ? this.form.clause_detail : '',
              allPrice: this.form.allPrice ? this.form.allPrice : 0,
              allCost: this.form.allCost ? this.form.allCost : 0,
              date: this.form.date ? this.form.date : '',

              uuid: this.uuid,
              frequency: 1,
              // uid: '10001',
              state: true

            }
            const tempData = Object.assign({}, temp)
            console.log(tempData)
            postData(this.baseUrl, 'post', '', tempData).then(response => {
              console.log(response);
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Create Successfully',
                type: 'success',
                duration: 1000
              })
              // // 創建成功後同步數據新增統計表
              // const statisticaTemp = {
              //   // id
              //   quotation: relationCreateReturn(response.id),
              //   company: relationCreateReturn(this.form.company.value),
              //   finalCost: response.allCost,
              //   profit: response.allPrice - response.allCost,
              //   allReceive: 0,
              //   content: []
              // }
              // const statisticaData = Object.assign({}, statisticaTemp)
              // postData(this.statisticalUrl, 'post', '', statisticaData).then(response => {
              //   // 創建成功後同步數據新增統計表
              //   this.dialogFormVisible = false
              //   this.$notify({
              //     title: 'Success',
              //     message: 'Create Successfully',
              //     type: 'success',
              //     duration: 1000
              //   })
              // })
            })
            this.$router.push({ path: '/quotation/quotation' })
          }
        })
      } else {
        this.$alert('產品內容未填寫，請檢查後重新提交', '提交錯誤', {
          confirmButtonText: '确定',
          callback: action => {
            this.$message({
              type: 'info',
              message: `action: ${action}`
            });
          }
        });
      }
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.baseUrl, 'delete', row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery._q = ''
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },
    // 新增或者修改时，新增电话或者手机的方法
    add() {
      this.form.content.push(this.baseContent)
    },
    // 新增或者修改时，刪除手機或者電話的方法
    deleteItem(delete_index) {
      this.form.content.splice(delete_index, 1)
    },
    // 遠程搜索 公司
    remoteMethod_companyOptions(query) {

      if (query) {
        this.companyQarams['filters[$and][0][name][$contains]'] = query
        this.companyQarams['pageSize'] = 999

      } else {
        delete this.companyQarams['filters[$and][0][name][$contains]']
      }
      // 獲取公司
      getData(this.companyUrl, this.companyQarams).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            country: res[index].country,
            address: res[index].address
          }
          arrList.push(arr)
        }
        this.companyOptions = arrList
        this.listLoading = false
      })

      console.log(this.companyOptions)
    },

    changeContent(item, index) {
      console.log('item', item.label);
      console.log('i', index);
      // this.$set(this.form.content, [index], item)
      this.$set(this.form.content, index,
        {
          "value": item.label.value,
          "label": item.label.label,
          "num": item.label.num,
          "price": item.label.price,
          "cost": item.label.cost,
          "describe": item.label.describe,
          "product_category": item.label.product_category,
          "product_category_id": item.label.product_category_id
        })
      console.log(this.form.content[index]);
    },
    // 遠程搜索 貨幣
    remoteMethod_currencyOptions() {
      this.getRelation(this.currencytUrl).then((result) => {
        const res = result
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            remark: res[index].remark
          }
          arrList.push(arr)
        }
        this.currencyOptions = arrList
      })
    },
    // 遠程搜索 聯繫人
    async remoteMethod_contactPersonOptions(query, target) {
      if (target) {
        this.contactPersonQarams['filters[$and][0][company][id][$eq]'] = target
        this.contactPersonQarams['pageSize'] = 999
      }
      if (query) {
        this.contactPersonQarams['filters[$and][0][name][$contains]'] = query
        this.contactPersonQarams['pageSize'] = 999
      } else {
        delete this.contactPersonQarams['filters[$and][0][name][$contains]']
      }
      await getData(this.contactPersonUrl, this.contactPersonQarams).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
          }
          arrList.push(arr)
        }
        this.contactPersonOptions = arrList
        this.listLoading = false
      })
    },

    // 遠程搜索 付款方式
    remoteMethod_paymentOptions() {
      this.getRelation(this.paymentUrl).then((result) => {
        const res = result
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
            remark: res[index].remark
          }
          arrList.push(arr)
        }
        this.paymentOptions = arrList
      })
    },
    // 遠程搜索 產品
    remoteMethod_product(query) {
      if (query !== '') {
        this.getRelation(this.productUrl, query).then((result) => {
          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              num: 1,
              price: res[index].price,
              cost: res[index].cost,
              describe: res[index].describe,
              // 產品分類
              product_category: res[index].product_category.name,
              product_category_id: res[index].product_category.id,
            }
            arrList.push(arr)
          }
          this.productOptions = arrList
        })
      } else {
        this.getRelation(this.companyUrl).then((result) => {

          const res = result
          const arrList = []
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const element = res[index]
            const arr = {
              value: res[index].id,
              label: res[index].name,
              price: res[index].price,
              cost: res[index].cost,
              describe: res[index].describe,
              // 產品分類
              product_category: res[index].product_category.name,
              product_category_id: res[index].product_category.id,
            }
            arrList.push(arr)
          }
          this.productOptions = arrList
        })
      }

      console.log(this.productOptions)
    },
    changeCompany(item) {
      this.$set(this.form, "address", item.address)
      // 重新選擇公司後清空聯繫人
      this.$set(this.form, "contact_person", '')
      // 獲取聯繫人
      this.remoteMethod_contactPersonOptions("", item.value)
    },

    changePayment(item) {
      this.$set(this.form, "paymentRemark", item.remark)
    },

    changeClause(item) {
      this.$set(this.form, "clause_detail", item.detail)
    },

    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = '總數';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (index === 5) {
            this.form.allPrice = sums[index];
            sums[index] = this.$options.filters['getArea'](sums[index]);
          }
          else if (index === 4) {
            sums[index] = '總價';
          }
          else if (index === 6) {
            this.form.allCost = sums[index];
            console.log(this.form.allCost);
            sums[index] = this.$options.filters['getArea'](sums[index]);
          }

        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    // 清除空的content
    clearContent() {
      for (let index = 0; index < this.form.content.length; index++) {
        const element = this.form.content[index];
        // 如果产品名称为空，则删除这个数据
        if (!element.label) {
          this.deleteItem(index);
        }
      }
    },
    // 獲取id來組成UUid
    async getUid() {
      this.listQuery = {
        page: 1,
        pageSize: 1,
        sort: 'id:DESC',
      }
      await getData(this.baseUrl, this.listQuery).then(response => {
        const res = response.results
        const arrList = []
        // 如果沒有單據，則從01開始新增
        if (res[0]) {
          // 把返回数据转换为table需要的格式
          for (let index = 0; index < res.length; index++) {
            const arr = {
              // id
              id: res[index].id,
              uuid: res[index].uuid,

            }
            arrList.push(arr)
          }
          let list = arrList[0]
          let numbers = list.uuid.match(/\d+/g);
          let num = parseInt(numbers[0])
          this.uuid = 'Q-' + fillZero(num + 1, 5) + "-01"
        } else {
          // 第一個uuid
          this.uuid = 'Q-00128-01'
        }
      })
    },
    // 提交按钮前的确认
    dialogConfirm(type) {
      this.$confirm(this.$t('table.goonPrompt'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        switch (type) {
          case 'create':
            this.createData()
            break;
          case 'add':
            this.createData('add')
            break;
          case 'updata':
            this.updateData()
            break;

          default:
            break;
        }

      }).catch(() => {
        this.$message({
          type: 'info',
          message:this.$t('table.cancelPrompt')
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.delete {
  position: absolute;
  top: 0;
  right: -50px;
}

.add {
  margin-top: 10px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: center;
}

.cost {
  color: red;
}

.describe {
  margin-top: 10px;
}
</style>
<style scoped>
/deep/.el-table .el-table__footer-wrapper :nth-child(7) {
  color: red;
}
</style>
