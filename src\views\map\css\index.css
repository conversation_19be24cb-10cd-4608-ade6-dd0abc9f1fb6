body {
  margin: 0;
  padding: 0;
}
#main {
  position: relative;
}
#map {
  position: relative;
}
.bg-box {
  position: absolute;
  width: 1839.65px;
  height: 700.2476781px;
  overflow: hidden;
  transition: 1s;
}
.bg {
  position: absolute;
  z-index: 2;
  width: 1839.65px;
  height: 700.2476781px;
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
}
.bg-box-1 {
  opacity: 1;
}
.bg-box-g {
  opacity: 1;
  top: 690px;
}
.floor-name {
  position: absolute;
  z-index: 2;
  top: 35%;
  right: 100px;
  font-size: 60px;
}
.icon {
  position: absolute;
  display: inline-block;
  z-index: 2;
  width: 32px;
  height: 32px;
  animation: fade-in-out 0.8s linear infinite;
}
.icon.icon-1-a {
  display: none;
  top: 296px;
  left: 780px;
}
.icon.icon-g-a {
  display: none;
  top: 260px;
  left: 852px;
}
.icon.icon-1-b {
  display: inline-block;
  top: 374px;
  left: 733px;
}
.icon.icon-g-b {
  display: inline-block;
  top: 375px;
  left: 731px;
}
.icon > img {
  width: 100%;
}
.bg-1 {
  background-image: url("../img/map-bg-1f.jpg");
}
.bg-g {
  background-image: url("../img/map-bg-gf.jpg");
}
#map {
  position: relative;
  z-index: 3;
  width: 1839.65px;
  height: 1400px;
}
#mySVG {
  width: 1839.65px;
  height: 1400px;
}
.btn-box {
  display: flex;
  z-index: 999;
  flex-wrap: nowrap;
  position: fixed;
  top: 80px;
  left: 20px;
}
.button {
  margin-right: 20px;
  width: 240px;
  height: 80px;
  background: #407efb;
  border: none;
  border-bottom: 6px solid #001fff;
  padding: 16px;
  border-radius: 4px;
  letter-spacing: 2px;
  color: #fff;
  font-weight: bold;
  font-family: 'Times New Roman', Times, serif;
  font-size: 18px;
  margin-top: 0px;
  transition: all 0.1s;
  outline: none;
}
.button:hover {
  margin-top: 2px;
  border-bottom-width: 3px;
  cursor: pointer;
}
.button:active {
  margin-top: 5px;
  border-bottom-width: 0px;
}
/* 打点部分 start*/
.point-box {
  z-index: 10;
}
.marker {
  position: absolute;
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ff6347;
  cursor: pointer;
  animation-duration: 0.3s;
  z-index: 20;
}
.marker:hover {
  transform: scale(1.2);
}
#upload {
  position: relative;
  z-index: 30;
}
/* 打点部分 end*/
#edit {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
}
.wave {
  width: 1839.65px;
  /* 水面宽度 */
  height: 500px;
  /* 水面高度 */
  overflow: hidden;
  position: absolute;
  top: 100px;
  left: 0;
  background-image: url(../img/wave.gif);
  background-size: cover;
  background-repeat: repeat;
  z-index: 1;
  transition: 0.2s;
}
.hide {
  display: none!important;
}
@keyframes fade-in-out {
  0% {
    opacity: 1;
  }
  30% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
  }
}
