<template>
  <div class="app-container">
    <!-- <h1>用户管理</h1> -->
    <h1>Users</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">{{ $t('table.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">{{ $t('table.reset') }}</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe>
      <el-table-column prop="name" label="Name" width="160" />
      <el-table-column prop="email" min-width="70px" label="Email" width="220" />
      <el-table-column prop="role" label="Role" width="220" />
      <el-table-column prop="stateType" label="State" />
      <el-table-column label="Operate">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{ $t('table.edit')
}}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
    $t('table.delete')
}}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>

    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />
    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="120px"
        style="width: 400px; margin-left:50px;">
        <el-form-item label="Name" prop="name">
          <el-input v-model="form.name" clearable />
        </el-form-item>
        <el-form-item label="Email" prop="email">
          <el-input v-model="form.email" clearable />
        </el-form-item>

        <el-form-item v-if="textMap[dialogStatus] == '修改'" label="Password" prop="password">
          <el-input v-model="form.password" clearable show-password />

        </el-form-item>

        <el-form-item label="Role" style="margin-top:36px;" prop="role">
          <el-select v-model="form.role" class="filter-item" placeholder="Please select">
            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('table.status')" style="margin-top:36px;">
          <el-select v-model="form.stateType" class="filter-item" placeholder="Please select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'

export default {
  name: 'Users',
  components: { Pagination },
  data() {
    let validatePassword = (rule, value, callback) => {
      var reg1 = /^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,}/; //密码必须是8位以上、必须含有字母、数字、特殊符号
      // var reg2 = /(123|234|345|456|567|678|789|012)/; //不能有3个连续数字
      if (!value) { //所以当没有值的时候，我们直接callback，让他不校验直接执行下一步
        return callback()
      }
      else if (!reg1.test(value)) {
        callback(new Error("The password must be more than 8 characters and must contain uppercase and lowercase letters"));
      }
      // else if (reg2.test(value)) {
      //   callback(new Error("不能有3个连续数字"));
      // }
      else {
        callback();
      }
    };
    return {
      baseUrl: 'admin/users/',
      list: [],
      totle: 0,
      listLoading: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // role
      roleOptions: [{
        value: '2',
        label: 'Jack'
      }, {
        value: '1',
        label: 'Queen'
      }, {
        value: '0',
        label: 'King'
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      form: {
        name: '',
        remark: '',
        state: ''
      },
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        _q: ''
      },
      rules: {
        name: [
          { required: true, message: 'please input', trigger: 'blur' }
        ],
        email: [
          { required: true, message: 'please input email', trigger: 'blur' },
          { type: 'email', message: 'please input email', trigger: ['blur', 'change'] }
        ],
        password: [
          { validator: validatePassword, trigger: 'blur' }
        ],
        role: [
          { required: true, message: 'please select', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.data.results
        const arrList = []
        console.log(res);
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            id: res[index].id,
            name: res[index].firstname,
            email: res[index].email,
            role: res[index].username,
            state: res[index].isActive,
            stateType: res[index].isActive ? this.$t('table.status1') : this.$t('table.status0'),
          }
          arrList.push(arr)
        }
        let filteredData = arrList.filter((item) => !/^liqi199315@gmail\.com$/i.test(item.email));
        this.list = filteredData
        this.total = response.data.pagination.total
        this.listLoading = false
      })
    },
    // 點擊編輯按鈕後的彈框
    handleClick(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      console.log(row.role);
      const rowForm = {
        id: row.id,
        name: row.name,
        email: row.email,
        password: row.password,
        detail: row.detail,
        roleType: row.roleType,
        role: row.role,
        stateType: row.stateType,
        state: row.stateType == '啟用'
      }
      this.form = Object.assign({}, rowForm) // copy obj

      console.log(this.form.state)
    },

    // 點擊編輯按鈕進行修改的事件
    updateData() {
      console.log('1111111');
      this.$refs['form'].validate((valid) => {
        console.log(this.form.stateType.value);
        console.log(this.form.stateType);
        if (valid) {
          const temp = {
            firstname: this.form.name,
            lastname: '',
            email: this.form.email,
            username: this.form.role.label,
            roles: [1],
            isActive: this.form.stateType.value == '1' || this.form.stateType =='啟用'
          }
          console.log(this.form);
          if (this.form.password) {
            temp.password = this.form.password
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'put', this.form.id, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 清空提交表
    resetForm() {
      this.form = {
        name: '',
        remark: '',
        state: true,
        stateType: '啟用'
      }
    },
    // 创建的按钮打开
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    // 點擊創建後的事件
    createData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const temp = {
            firstname: this.form.name,
            lastname: '',
            email: this.form.email,
            detail: this.form.detail,
          }
          console.log(temp.state)
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.baseUrl, 'delete', row.id, '').then(() => {
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },
    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery._q = ''
      this.getList(this.listQuery)
    },

    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}
</style>
