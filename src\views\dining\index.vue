<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1> Dining </h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">

      <el-form-item label="">
        <el-input v-model="listQuery['name']" :placeholder="$t('table.queryCompany')" />
      </el-form-item>
      <el-form-item>
        <el-form-item label="">
          <el-input v-model="listQuery['num']" placeholder="門牌號" />
        </el-form-item>
        <el-form-item label="">
          <el-select v-model="listQuery['newshop']" clearable placeholder="newshop">
            <el-option :key="1" label="是" :value="true">
            </el-option>
            <el-option :key="2" label="否" :value="false">
            </el-option>
          </el-select>
          <el-select v-model="listQuery['comingsoon']" clearable placeholder="comingsoon">
            <el-option :key="1" label="是" :value="true">
            </el-option>
            <el-option :key="2" label="否" :value="false">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-select v-model="listQuery['mall']" clearable placeholder="mall">
            <el-option :key="0" value="YOHO MALL I">
            </el-option>
            <el-option :key="1" value="YOHO MALL II">
            </el-option>
            <el-option :key="2" value="YOHO MIX">
            </el-option>
            <el-option :key="3" value="YOHO PLUS">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">{{ $t('table.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">{{ $t('table.reset') }}</el-button>
        <!-- <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button> -->
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="img" label="圖片" width="140">
        <template slot-scope="{row}">
          <a :href='row.image' target="_blank">
            <img class="avatar" :src="row.image" alt="">
          </a>
          <img v-if="row.newshop" class="newicon" src="../../assets/new.png" alt="">
          <img v-if="row.comingsoon" class="newicon" style="top: auto;left: auto;right: 0;bottom: 0;width: 60%;"
            src="../../assets/coming_soon.png" alt="">
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名稱（英文）" width="" />
      <el-table-column prop="name_tc" label="名稱（繁體）" width="" />
      <el-table-column prop="num" label="店鋪號" width="" />
      <el-table-column prop="floor" label="樓層" width="" />
      <el-table-column prop="phone" label="電話" width="" />

      <el-table-column prop="business_hour" label="Open Time" width="140">
        <template slot-scope="{row}">
          <p>{{ row.business_hour.en }}</p>
          <p>{{ row.business_hour.sc }}</p>
          <p>{{ row.business_hour.tc }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="mall" label="Mall" width="" />
      <el-table-column prop="modelName" label="地圖ID" width="" />
      <!-- <el-table-column prop="shoptype" label="類型" width="140">
        <template slot-scope="{row}">
          {{ row.shoptype.name }}
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{
            $t('table.edit')
          }}</el-button>
          <!-- <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm> -->
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <el-form-item label="名稱（英文）" prop="name">
          <span>{{ form.name }}</span>
        </el-form-item>
        <el-form-item label="名稱（繁體）" prop="name_tc">
          <span>{{ form.name_tc }}</span>
        </el-form-item>
        <el-form-item label="名稱（簡體）" prop="name_zh">
          <span>{{ form.name_zh }}</span>
        </el-form-item>

        <el-form-item label="門牌號" prop="num">
          <span>{{ form.num }}</span>
        </el-form-item>

        <el-form-item label="楼层" prop="floor">
          <span>{{ form.floor }}</span>
        </el-form-item>

        <el-form-item label="Mall" prop="mall">
          <span>{{ form.mall }}</span>
        </el-form-item>


        <el-form-item label="Logo" prop="image">
          <img class="avatar" :src="form.image" alt="">
        </el-form-item>
        <el-form-item label="newShop" prop="newshop">
          <el-switch v-model="form.newshop" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="comingSoon" prop="comingsoon">
          <el-switch v-model="form.comingsoon" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="地圖ID" prop="modelName">
          <el-input v-model="form.modelName" />
          <el-button type="primary" @click="openExternalProject">open map</el-button>

        </el-form-item>



      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog width="80%" title="選點" :visible.sync="dialogMapVisible">
      <!-- <h2>{{ modelId }}</h2> -->
      <div>
        <el-button @click="dialogMapVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="modelFunc">
          確認
        </el-button>
      </div>
    </el-dialog>

    <el-dialog title="Map" :visible.sync="dialogVisible" width="80%">
      <iframe id="iframe" src="https://demo.hkeshowcase.com/yohomapedit/" style="width: 100%; height: 500px; border: none;"></iframe>
      <el-button type="primary" @click="sendMsg">確定</el-button>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData, uploadData } from '@/api/requestData'
import { getDataCms } from '@/api/requestCms'
import Pagination from '@/components/Pagination'
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

export default {
  name: 'Product',
  components: { Pagination, ImageUploader },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: "shop-expand", // 变量修改，请求主题名称
      listLoading: false,
      btnLoading: false,

      modelId: "",
      // 弹框
      dialogFormVisible: false,
      // 地圖彈框
      dialogMapVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      dialogVisible: false,
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        // newshop: true,
        // sort: 'id:ASC',
        _q: ''
      },
      statusOptions: [],
      // 楼层选项
      floorOptions: [
        {
          value: "1",
          label: "GF"
        }, {
          value: "2",
          label: "L1"
        }, {
          value: "3",
          label: "L2"
        }, {
          value: "4",
          label: "L3"
        },
      ],

      resetTrigger: 0, // 控制重置逻辑的状态变量 ImageUploader
      // 变量修改，表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_tc: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_zh: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        num: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        floor: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        mall: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],

        initials: [
          { required: true, message: '請輸入單個英文字母', trigger: 'blur' },
          {
            validator: this.validateLetter,
            trigger: 'blur'
          }
        ],
        shoptype: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  mounted() {
    // 监听来自 iframe 的消息
    window.addEventListener("message", this.handleMessage);

  },
  beforeDestroy() {
    // 移除消息监听
    window.removeEventListener("message", this.handleMessage);
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getDataCms(`apis/dinings`, this.listQuery).then(response => {
        const res = response.data
        this.list = res
        console.log(this.list);
        this.total = response.meta.pagination.total
        this.listLoading = false
      })
    },
    // 这是地图的方法
    openExternalProject() {
      this.dialogVisible = true;
    },
    openExternalProject() {
      this.dialogVisible = true;
    },
    sendMsg() {
      // 检查 iframe 对象是否存在
      const iframe = document.getElementById('iframe');
      if (iframe && iframe.contentWindow) {
        // 执行你的操作
        iframe.contentWindow.postMessage({ action: "getData", payload: "your payload" }, "*");
      } else {
        console.error('Cannot access iframe contentWindow: iframe is null or contentWindow is undefined');
      }
      setTimeout(() => {
        this.dialogVisible = false
      }, 500);
    },
    handleMessage(event) {
      // 处理来自 iframe 的消息
      // 确保消息来自预期的 iframe
      if (event.source !== iframe.contentWindow) {
        return;
      }

      // 处理来自 iframe 的消息
      const data = event.data;
      if (data.action === "sendResult") {
        const result = data.payload;
        console.log("Received result from iframe:", result);
        // 在这里执行相应的操作
        this.form.modelName = result
      }
    },

    validateLetter(rule, value, callback) {
      if (/^[A-Za-z]{1}$/.test(value)) {
        callback();
      } else {
        callback(new Error('請輸入單個英文字母'));
      }
    },

    /*
    上傳文件前的处理 ImageUploader
    */
    handleUploadSuccess({ id, type }) {
      // 使用传递的type动态更新相应的状态
      console.log(`${type}:`, id);
      this.form[`${type}`] = id;
      console.log(this.form);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    /*
    點擊修改的事件
    */
    handleClick(row) {
      // this.listLoading = true
      console.log(row);
      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      // 变量修改时的参数
      this.formRow = {
        id: row.hid,
        uuid: (row.id).toString(),
        name: row.name,
        name_tc: row.name_tc,
        name_zh: row.name_zh,
        floor: row.floor,
        mall: row.mall,
        num: row.num,
        image: row.image,
        newshop: row.newshop,
        comingsoon: row.comingsoon,
        modelName: row.modelName,


        // modelId: row.modelId,
        // img: row.img.url,
        // description: row.description,
        // description_tc: row.description_tc,
        // description_zh: row.description_zh,
      }

      this.form = Object.assign({}, this.formRow) // copy obj
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let form = this.form
          let methods
          let reqId = ""
          if (form.id) {
            methods = 'put'
            reqId = form.id
          } else {
            methods = 'post'
          }

          const tempData = Object.assign({}, form)
          postData(this.apiName, methods, reqId, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    清空提交表
    */
    resetForm() {
      // 变量修改，清零配置
      this.form = {
        name: "",
        name_tc: "",
        name_zh: "",
        floor: "",
        mall: "",
        num: "",
        initials: "",
        modelId: "",
        newshop: "",
        comingsoon: "",
        modelName: "",
      }

      // 清空組件狀態 ImageUploader
      this.triggerReset()
    },
    // ImageUploader 重置器
    triggerReset() {
      this.resetTrigger += 1; // 每次调用时改变值，触发ImageUploader的重置
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          if (this.form.img) {

            // 如果状态为0，则为空，否则就有内容，变量修改type
            let pdf_type = this.connectFunc(parseInt(this.form.shoptype.value), '')

            // 重设pdf_type，变量修改type
            this.form.shoptype = pdf_type
            // 重设floor，变量修改floor
            let floor = this.form.floor.label
            this.form.floor = floor

            // 设定通告的类型
            const temp = this.form
            const tempData = Object.assign({}, temp)
            console.log(tempData)
            // 按鈕開始加載
            this.btnLoading = true
            postData(this.apiName, 'post', '', tempData).then(() => {
              this.getList(this.listQuery)
              // 按鈕停止加載並關閉彈框
              this.btnLoading = false
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Create Successfully',
                type: 'success',
                duration: 1000
              })
            })
          } else {
            this.$notify({
              title: 'Warning',
              message: '請先上傳文件',
              type: 'success',
              duration: 1000
            })

          }
        }
      })
    },

    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 50,
        _q: ''
      }
      this.getList(this.listQuery)
    },

    // 获取首字母的方法
    generateInitials() {
      const name = this.form.name.trim();
      if (name === '') {
        this.form.initials = '';
      } else if (/^[A-Za-z]/.test(name)) {
        this.form.initials = name.charAt(0).toUpperCase();
      } else {
        this.form.initials = '1';
      }
    },

    // map的数据返回
    handleValueChanged(value) {
      this.modelId = value;
    },
    // 賦值modelId的方法
    modelFunc() {
      this.dialogMapVisible = false
      this.form.modelId = this.modelId
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}

.avatar {
  width: 120px;
}


.newicon {
  width: 30%;
  position: absolute;
  top: 0;
  left: 0;

}
</style>
