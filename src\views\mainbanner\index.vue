<template>
  <div class="app-container">
    <!-- <h1>產品列表</h1> -->
    <h1>{{ $t('route.mainbanner') }}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">

      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe v-loading="listLoading">
      <!-- <el-table-column prop="id" label="ID" width="80" /> -->
      <el-table-column label="#" width="60" column-align="center">
        <template slot-scope="scope">
          <SPAN>{{ scope.$index + 1 }}</SPAN>
        </template>
      </el-table-column>
      <el-table-column prop="img" label="圖片" width="140">
        <template slot-scope="{row}">
          <a :href='baseURL + row.url' target="_blank">
            <img class="avatar" :src="baseURL + (row.formats ? row.formats.thumbnail.url : row.url)" alt="">
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="海報名稱" width="" />
      <el-table-column prop="ext" label="格式" width="" />
      <el-table-column prop="name" :label="$t('route.resolution')" width="">
        <template slot-scope="{row}">
          <span>{{ row.width ? row.width +" × "+ row.height :"" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="Size" width="">
        <template slot-scope="{row}">
          <span>{{ (row.size /1000).toFixed(2) +"MB" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="">
        <template slot-scope="scope">

          <el-button type="primary" size="small">
            <a :href='baseURL + scope.row.url' target="_blank">查看</a>
          </el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{
              $t('table.delete')
            }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="150px"
        style="width: 480px; margin-left:50px;">

        <!-- <el-form-item label="通告名稱（英文）" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        -->

        <el-form-item label="文件" prop="file">
          <div v-loading="uploadLoading">
            <el-upload class="upload-demo" action="/upload/" :limit="1" :before-upload="handleBeforeUpload"
              accept="video/MPEG,video/MP4,video/WMV,video/AVI,video/FLV,image/JPEG,image/PNG,image/GIF,image/SVG,image/TIFF"
              :show-file-list="false">
              <el-button size="small" type="primary">選擇文件</el-button><span class="fileName">{{ fileName }}</span>

              <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
            </el-upload>
            <el-button size="small" @click="uploadFile" v-if="!fileUploadType && fileName"
              style="margin-top:10px">上傳文件</el-button>
            <el-tag type="success" v-if="fileUploadType">文件上傳成功</el-tag>

          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button :loading="btnLoading" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import axios from "axios"
import { getData, postSingleData, uploadData } from '@/api/requestData'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  filters: {
    getArea: function (area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      baseURL: "",
      apiName: "screen",
      listLoading: false,
      uploadLoading: false,
      btnLoading: false,
      file: "",
      fileName: "",
      fileId: "",
      fileUploadType: false,
      // 視頻有哪些
      mediaList: [],
      // 弹框
      dialogFormVisible: false,
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 50,
        sort: 'id:ASC',
        _q: ''
      },

      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_tc: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        name_zh: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  methods: {
    /*
    获取列表数据
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res[0].media
        console.log(this.list);
        // this.total = response.pagination.total
        this.listLoading = false
      })
    },
    /*
    上傳文件前的处理
    */
    handleBeforeUpload(file) {
      const acceptedFormats = /(video\/(MPEG|MP4|WMV|AVI|FLV))|(video\/(mpeg|mp4|wmv|avi|flv))|(image\/(JPEG|PNG|GIF|SVG|TIFF))|(image\/(jpeg|png|gif|svg|tiff))/;
      const isType = acceptedFormats.test(file.type)
      if (!isType) {
        this.$message.error("只能上傳圖片或者視頻");
      } else {
        this.file = file
        this.fileName = file.name
      }
    },
    /*
    上傳文件
    */
    uploadFile() {
      // 上傳加載中
      this.uploadLoading = true
      const formData = new FormData();
      formData.append('files', this.file);

      uploadData(formData).then(response => {
        // loading結束，設定已完成上傳，設定fileID
        this.uploadLoading = false
        this.fileUploadType = true
        this.fileId = response[0].id
        console.log(this.fileId);
        this.$notify({
          title: '上傳成功',
          message: `文件${this.fileName}`,
          type: 'success'
        })
      })
        .catch(error => {
          this.uploadLoading = false
          console.log('上传图片失败')
          this.$notify({
            title: '上傳失敗',
            message: `文件：${this.fileName}`,
            type: 'error'
          })
          console.log(error);
        });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    /*
    點擊修改的事件
    */
    handleClick(row) {
      // this.listLoading = true
      this.resetForm()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      let pdf_type
      if (row.pdf_type) {
        pdf_type = {
          value: row.pdf_type.id + '',
          label: row.pdf_type.name_tc
        }
      } else {
        pdf_type = {
          value: '0',
          label: "草稿"
        }

      }
      this.fileName = row.file.name
      this.formRow = {
        id: row.id,
        name: row.name,
        name_tc: row.name_tc,
        name_zh: row.name_zh,
        file: row.file.id,
        stateType: pdf_type
      }
      this.form = Object.assign({}, this.formRow) // copy obj
    },
    /*
    確認修改的事件
    */
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let form = this.form
          console.log(this.formRow);
          let pdf_type = this.connectFunc(parseInt(this.form.stateType.value), parseInt(this.formRow.stateType.value))

          console.log(this.form);
          this.form.pdf_type = pdf_type
          // 设定pdf的id
          this.form.file = parseInt(this.fileId)
          delete this.form.stateType
          const tempData = Object.assign({}, form)
          console.log(tempData)
          postData(this.apiName, 'put', tempData.id, tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },

    /*
    清空提交表
    */
    resetForm() {
      this.form = {
      }
      this.file = ""
    },
    /*
    创建的按钮打开
    */
    handleCreate() {
      this.resetForm()
      let mediaList = this.list.map((item) => {
        return {
          id: item.id
        }
      })
      this.form.media = mediaList
      this.dialogStatus = 'create'
      this.dialogFormVisible = true

      // 文件上傳部分的清零
      this.file = ""
      this.fileId = ""
      this.fileName = ""
      this.fileUploadType = false
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },

    /*
    提交創建的事件
    */
    createData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.fileId) {
            let newmedia = {
              id: parseInt(this.fileId)
            }
            // 设定pdf的id
            this.form.media.push(newmedia)
            const temp = this.form
            const tempData = Object.assign({}, temp)
            console.log(tempData)
            // 按鈕開始加載
            this.btnLoading = true
            postSingleData(this.apiName, 'put', '', tempData).then(() => {
              this.getList()
              // 按鈕停止加載並關閉彈框
              this.btnLoading = false
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Create Successfully',
                type: 'success',
                duration: 1000
              })
            })
          } else {
            this.$notify({
              title: 'Warning',
              message: '請先上傳文件',
              type: 'success',
              duration: 1000
            })

          }
        }
      })
    },

    /*
    管理關聯表表的方法
    */
    connectFunc(newId, oldId) {
      if (newId) {
        if (oldId) {
          if (oldId == newId) {
            return {
              "disconnect": [],
              "connect": []
            }
          } else {

            return {
              "disconnect": [
                {
                  "id": parseInt(oldId)
                }],
              "connect": [
                {
                  "id": parseInt(newId),
                  "position": {
                    "end": true
                  }
                }
              ]
            }
          }

        } else {
          return {
            "disconnect": [],
            "connect": [
              {
                "id": parseInt(newId),
                "position": {
                  "end": true
                }
              }
            ]
          }
        }

      } else if (oldId) {
        return {
          "disconnect": [
            {
              "id": parseInt(oldId),
              "position": {
                "end": true
              }
            }],
          "connect": []
        }
      }
      else {
        return {
          "disconnect": [],
          "connect": []
        }
      }


    },
    // 删除的最后确认按钮
    deleteDialog(scope) {
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {

        this.resetForm()
        let mediaList = this.list.map((item) => {
          return {
            id: item.id
          }
        })
        this.form.media = mediaList
        this.$message({
          type: 'info',
          message: this.$t('table.deleteSuccess')
        })
        this.form.media.splice(scope.$index, 1)
        console.log(this.form)
        // 设定pdf的id
        const temp = this.form
        const tempData = Object.assign({}, temp)
        console.log(tempData)
        // 按鈕開始加載
        this.btnLoading = true
        postSingleData(this.apiName, 'put', '', tempData).then(() => {
          this.getList()
          // 按鈕停止加載並關閉彈框
          this.btnLoading = false
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Create Successfully',
            type: 'success',
            duration: 1000
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {

  // width: 420px;
}
</style>
