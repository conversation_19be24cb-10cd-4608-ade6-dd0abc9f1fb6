<template>
  <div class="app-container">
    <!-- <h1>公司</h1> -->
    <h1>{{ $t('route.company') }}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">{{ $t('table.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">{{ $t('table.reset') }}</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{ $t('table.add') }}</el-button>
        <el-button type="primary" plain icon="el-icon-download" @click="handleDownload()">Export Excel</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe @sort-change="handlesortChange">
      <el-table-column prop="id" label="Id" width="40" sortable="custom" />
      <el-table-column prop="name" :label="$t('table.companyName')" width="160" sortable="custom" />
      <el-table-column prop="company_classification" :label="$t('table.category')" width="140" sortable="custom" />
      <el-table-column prop="country" :label="$t('table.country')" sortable="custom" width="80" />
      <el-table-column prop="address" :label="$t('table.address')" width="170" />
      <el-table-column prop="telephone" :label="$t('table.tel')" width="120" />
      <el-table-column prop="website" :label="$t('table.website')" width="160" />
      <el-table-column prop="remark" :label="$t('table.remark')" width="150" />
      <el-table-column prop="updatedAt" width="140" :label="$t('table.editDate')" />
      <el-table-column prop="updatedBy" :label="$t('table.editor')" width="70" />
      <el-table-column prop="stateType" :label="$t('table.status')" width="70" />
      <el-table-column :label="$t('table.operate')" width="200">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small"
            @click="handleClick(scope.row)">{{ $t('table.edit') }}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')"
            icon="el-icon-info" icon-color="red" :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete"
              style="margin-left:12px">{{ $t('table.delete') }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>
    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />

    <el-dialog :title="textMap[dialogStatus] == '修改' ? $t('table.edit') : $t('table.create')"
      :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="120px"
        style="width: 400px; margin-left:50px;">

        <el-form-item :label="$t('table.companyName')" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>

        <el-form-item label="類型" prop="company_classification">
          <el-select v-model="form.company_classification" class="filter-item" placeholder="Please select">
            <el-option v-for="item in company_classificationOptions" :key="item.value" :label="item.label"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('table.country')" prop="country">
          <el-select v-model="form.country" class="filter-item" placeholder="Please select">
            <el-option v-for="item in countryOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('table.address')">
          <el-input v-model="form.address" />
        </el-form-item>
        <el-form-item :label="$t('table.tel')">
          <el-input v-model="form.telephone" />
        </el-form-item>
        <el-form-item :label="$t('table.website')">
          <el-input v-model="form.website" />
        </el-form-item>
        <el-form-item :label="$t('table.remark')">
          <el-input v-model="form.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
        <el-form-item :label="$t('table.status')" prop="stateType">
          <el-select v-model="form.stateType" class="filter-item" placeholder="Please select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create') : $t('table.edit') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import { relationReturn, relationCreateReturn } from '@/api/tablefunction'
import Pagination from '@/components/Pagination'

export default {
  name: 'Product',
  components: { Pagination },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::company.company/',
      company_classificationUrl: '/content-manager/collection-types/api::company-classification.company-classification/',
      countrynUrl: '/content-manager/collection-types/api::country.country/',
      list: [],
      totle: 0,
      listLoading: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      // 產品分類
      company_classificationOptions: [],
      // 國家選項
      countryOptions: [],
      form: {
        id: '',
        // 公司名稱
        name: '',
        // 公司類型
        company_classification: '',
        company_classification_id: '',
        // 地址
        address: '',
        // 國家
        country: '',
        country_id: '',
        // 備註
        remark: '',
        // 電話
        telephone: '',
        // 網站
        website: '',
        // 狀態
        state: '',
        stateType: '',
        // 修改時間
        updatedAt: '',
        // 修改人
        updatedBy: ''
      },
      formRow: {},
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '請輸入', trigger: 'blur' }
        ],
        // 公司類型
        company_classification: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 國家
        country: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ],
        // 狀態
        stateType: [
          { required: true, message: '請選擇', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList(this.listQuery)
    // 獲取公司分類
    this.getCompanyClassification()
    // 獲取國家分類
    this.getCountry()
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      console.log(params)
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            // 公司id
            id: res[index].id,
            // 公司名稱
            name: res[index].name,
            // 公司類型
            company_classification: res[index].company_classification ? res[index].company_classification.name : '',
            company_classification_id: res[index].company_classification ? res[index].company_classification.id : '',
            // 地址
            address: res[index].address,
            // 國家
            country: res[index].country ? res[index].country.name : '',
            country_id: res[index].country ? res[index].country.id : '',
            // 備註
            remark: res[index].remark,
            // 電話
            telephone: res[index].telephone,
            // 網站
            website: res[index].website,
            // 狀態
            state: res[index].state,
            stateType: res[index].state ? this.$t('table.status1') : this.$t('table.status0'),
            // 修改時間
            updatedAt: timeChange(res[index].updatedAt),
            // 修改人
            updatedBy: res[index].updatedBy ? res[index].updatedBy.firstname : 'null'
          }
          console.log(arr)
          arrList.push(arr)
        }
        this.list = arrList
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    // 獲取公司分類
    getCompanyClassification(p) {
      let params = {
        'pageSize': 999
      }

      this.listLoading = true
      getData(this.company_classificationUrl, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name
          }
          arrList.push(arr)
        }
        this.company_classificationOptions = arrList
        this.listLoading = false
        console.log(this.company_classificationOptions)
      })
    },
    // 排序的方法
    handlesortChange(column) {
      let target = ''
      switch (column.prop) {
        case 'company_classification':
          target = 'company_classification.name'
          break;
        case 'country':
          target = 'country.name'
          break;
        case 'name':
          target = 'name'
          break;
        case 'id':
          target = 'id'
          break;

        default:
          break;
      }
      if (column.order == "ascending") {
        this.listQuery.sort = target + ":ASC"
      }
      if (column.order == "descending") {
        this.listQuery.sort = target + ":DESC"
      }
      this.getList(this.listQuery)
    },
    // 獲取國家
    getCountry(p) {
      let params = {
        'pageSize': 999
      }

      this.listLoading = true
      getData(this.countrynUrl, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            value: res[index].id,
            label: res[index].name,
          }
          arrList.push(arr)
        }
        this.countryOptions = arrList
        this.listLoading = false
        console.log(this.countryOptions)
      })
    },
    // 點擊修改的事件
    handleClick(row) {
      // this.listLoading = true
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      console.log(row)
      const rowForm = {
        // 公司id
        id: row.id,
        // 公司名稱
        name: row.name,
        // 公司類型
        company_classification: row.company_classification,
        company_classification_id: row.company_classification_id,
        // 地址
        address: row.address,
        // 國家
        country: row.country,
        country_id: row.country_id,
        // 備註
        remark: row.remark,
        // 電話
        telephone: row.telephone,
        // 網站
        website: row.website,
        // 狀態
        state: row.state,
        stateType: row.state ? '啟用' : '關閉',
        // 修改時間
        updatedAt: timeChange(row.updatedAt),
        // 修改人
        updatedBy: row.updatedBy.firstname
      }
      this.form = Object.assign({}, rowForm) // copy obj
      console.log(this.form)
    },
    // 確認修改的事件
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const temp = {
            id: this.form.id,
            name: this.form.name ? this.form.name : '',
            address: this.form.address ? this.form.address : '',
            // 如果是關聯表，則用方法去判斷顯示什麼
            company_classification: relationReturn(this.form.company_classification.value, this.form.company_classification_id),
            country: relationReturn(this.form.country.value, this.form.country_id),
            telephone: this.form.telephone ? this.form.telephone : '',
            website: this.form.website ? this.form.website : '',
            remark: this.form.remark ? this.form.remark : '',
            state: this.form.stateType.value != '0'
          }

          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'put', temp.id, tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 清空提交表
    resetForm() {
      this.form = {
        id: '',
        // 公司名稱
        name: '',
        // 公司類型
        company_classification: '',
        company_classification_id: '',
        // 地址
        address: '',
        // 國家
        country: '',
        country_id: '',
        // 備註
        remark: '',
        // 電話
        telephone: '',
        // 網站
        website: '',
        // 狀態
        state: '',
        stateType: '',
        // 修改時間
        updatedAt: '',
        // 修改人
        updatedBy: ''
      }
    },
    // 创建的按钮打开
    handleCreate() {
      this.resetForm()
      this.form.stateType = '啟用'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    // 提交創建的事件
    createData() {
      this.$refs['form'].validate((valid) => {
        console.log(this.form)
        if (valid) {
          const temp = {
            name: this.form.name ? this.form.name : '',
            address: this.form.address ? this.form.address : '',
            telephone: this.form.telephone ? this.form.telephone : '',
            website: this.form.website ? this.form.website : '',
            company_classification: relationCreateReturn(this.form.company_classification.value),
            country: relationCreateReturn(this.form.country.value),
            remark: this.form.remark ? this.form.remark : '',
            state: this.form.stateType.value != '0'

          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'post', '', tempData).then(() => {
            this.getList()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.baseUrl, 'delete', row.id, '').then(() => {
          this.getList()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },

    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery._q = ''
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },
    // 導出Excel
    handleDownload() {
      this.$confirm(this.$t('table.goonPrompt'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['ID', this.$t('table.companyName'), this.$t('table.category'), this.$t('table.country'), this.$t('table.address'), this.$t('table.tel'), this.$t('table.website'), this.$t('table.remark'), this.$t('table.editDate'), this.$t('table.editor'), this.$t('table.status')]
          const filterVal = ['id', 'name', 'company_classification', 'country', 'address', 'telephone', 'website', 'remark', 'updatedAt', 'updatedBy', 'stateType']
          const data = this.formatJson(filterVal, this.list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.$t('route.company'),
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.cancelPrompt')
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}
</style>
