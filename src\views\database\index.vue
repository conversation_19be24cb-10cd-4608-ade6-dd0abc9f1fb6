<template>
  <div class="app-container documentation-container">
    <h1>{{msg}}</h1>
  </div>
</template>

<script>
import DropdownMenu from '@/components/Share/DropdownMenu'

export default {
  name: 'Documentation',
  components: { DropdownMenu },
  data() {
    return {
      msg:"this is a message"
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      productCategories().then(response => {
        // this.list = response.data.items
        // this.total = response.data.total
        console.log(response);

      })
    }
  }
}

</script>

<style lang="scss" scoped>

</style>
